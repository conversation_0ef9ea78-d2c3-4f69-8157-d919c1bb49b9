# Audio Management Implementation

## Overview

This document describes the enhanced audio management system implemented for the 360-degree viewer, providing continuous audio playback with intelligent pause/resume functionality for popup interactions.

## Features Implemented

### 1. Continuous Audio Playback ✅
- **Background audio plays continuously** without restarting when users switch between different 360-degree textures or scenes
- **Audio element persistence** - prevents recreation of HTML5 Audio element on component re-renders
- **Seamless transitions** - maintains playback during scene changes and texture loading

### 2. Audio Pause/Resume for Info Popups ✅
- **Automatic pause** when any of these popup types open:
  - Info video marker popups
  - Info document marker popups
  - Video player popups
- **Automatic resume** when popups are closed
- **Playback position preservation** - audio resumes from exact position where it was paused

### 3. Advanced Popup Management ✅
- **Multiple popup support** - handles multiple simultaneous popups gracefully
- **Smart resume logic** - only resumes audio when ALL popups are closed
- **Edge case handling** - manages rapid popup opening/closing without audio glitches

### 4. Advanced Audio Preloading & Caching ✅ **NEW**
- **Early preloading** - audio files downloaded during website initialization
- **Memory caching** - immediate availability when playback is needed
- **Service Worker caching** - persistent storage across browser sessions
- **Retry logic** - robust loading with exponential backoff
- **Cache management** - automatic cleanup and statistics
- **Fallback handling** - graceful degradation if preloading fails

## Architecture

### Core Components

#### 1. Enhanced AmbientAudio Component (`src/components/360s/AmbientAudio.jsx`)
```javascript
// Key features:
- forwardRef implementation for external control
- useImperativeHandle for exposing audio controls
- External pause/resume via isPaused prop
- Prevents audio element recreation on re-renders
- Maintains playback position during pause
- Integrates with audio preloader for cached audio
```

#### 2. AudioContext Provider (`src/contexts/AudioContext.jsx`)
```javascript
// Centralized audio state management:
- Global audio enable/disable state
- Active popup tracking with Set data structure
- Audio reference registration
- Coordinated pause/resume operations
- Audio preloading status tracking
- Cache statistics monitoring
```

#### 3. Audio Preloader System (`src/lib/audio-preloader.js`) **NEW**
```javascript
// Advanced preloading and caching:
- Early audio file downloading during app initialization
- Memory cache with configurable timeout
- Retry logic with exponential backoff
- URL resolution for production compatibility
- Cache statistics and management
```

#### 4. Service Worker Caching (`public/audio-cache-sw.js`) **NEW**
```javascript
// Persistent audio caching:
- Browser-level audio file caching
- Cache-first serving strategy
- Automatic cache management
- Cross-session persistence
```

#### 5. AudioPreloader Component (`src/components/AudioPreloader.jsx`) **NEW**
```javascript
// Early initialization component:
- Starts preloading immediately on app load
- Optional progress display for development
- Preload status tracking and callbacks
```

#### 6. Updated Experience Reducer (`src/contexts/reducerExperience.js`)
```javascript
// New actions added:
- PAUSE_AUDIO: For popup-induced pauses
- RESUME_AUDIO: For popup-induced resumes
- Removed playAudio:false from popup actions
```

### Integration Points

#### 1. 360Viewer Component
- Wrapped with AudioContextProvider
- Uses audio context for state management
- Registers audio ref with context
- Conditional rendering based on audio state

#### 2. Popup Components
- GeneralPopupWrapper: Handles document/image popups
- VideoPopupWrapper: Handles video popups
- Automatic popup registration/deregistration via useEffect

#### 3. Sound Button Component
- Updated to use AudioContext instead of reducer
- Maintains backward compatibility
- Visual state reflects actual audio state

## Technical Implementation Details

### Audio State Management
```javascript
// Audio states tracked:
- isAudioEnabled: User preference (sound button)
- isAudioPaused: Popup-induced pause state
- activePopups: Set of currently open popup IDs
- shouldAudioPlay: Computed state (enabled && no popups)
```

### Popup Lifecycle
```javascript
// When popup opens:
1. addPopup(popupId) called
2. If first popup, audio.pause() triggered
3. Audio position preserved

// When popup closes:
1. removePopup(popupId) called  
2. If last popup, audio.resume() triggered
3. Audio continues from saved position
```

### Texture Switching Optimization
```javascript
// Prevents audio restarts:
- Audio element created only once
- Component re-renders don't recreate audio
- Texture changes don't affect audio state
- Continuous playback maintained
```

## Usage Examples

### Basic Integration
```jsx
// Wrap your 360 viewer with AudioContextProvider
<AudioContextProvider>
  <ThreeSixtyViewer id="scene-1" />
</AudioContextProvider>
```

### Early Audio Preloading
```jsx
// Add to your root layout for immediate preloading
import AudioPreloader from '@/components/AudioPreloader';

function RootLayout({ children }) {
  return (
    <div>
      <AudioPreloader
        showProgress={process.env.NODE_ENV === 'development'}
        onPreloadComplete={(stats) => console.log('Audio ready:', stats)}
      />
      {children}
    </div>
  );
}
```

### Custom Popup Integration
```jsx
function CustomPopup() {
  const { addPopup, removePopup } = useAudioContext();

  useEffect(() => {
    addPopup('custom-popup');
    return () => removePopup('custom-popup');
  }, []);

  return <div>Custom popup content</div>;
}
```

### Audio Control with Preload Status
```jsx
function AudioControls() {
  const {
    toggleAudio,
    isAudioEnabled,
    audioState,
    audioPreloadStatus,
    getAudioStats
  } = useAudioContext();

  const handleStatsClick = () => {
    const stats = getAudioStats();
    console.log('Audio Statistics:', stats);
  };

  return (
    <div>
      <button onClick={toggleAudio}>
        {isAudioEnabled ? 'Mute' : 'Unmute'}
        ({audioState.isPlaying ? 'Playing' : 'Paused'})
      </button>

      <div className="audio-status">
        Preload: {audioPreloadStatus}
        <button onClick={handleStatsClick}>Show Stats</button>
      </div>
    </div>
  );
}
```

### Manual Audio Preloading
```jsx
import { preloadAudio, getCachedAudio } from '@/lib/audio-preloader';

// Preload specific audio file
const audio = await preloadAudio('/assets/custom-audio.mp3', {
  id: 'custom-audio',
  maxRetries: 3,
  timeout: 20000
});

// Get cached audio if available
const cachedAudio = getCachedAudio('/assets/custom-audio.mp3');
if (cachedAudio) {
  await cachedAudio.play();
}
```

## Testing

### Test Coverage
- ✅ Continuous audio during texture switching
- ✅ Pause/resume on popup open/close  
- ✅ Multiple popup scenarios
- ✅ User audio toggle functionality
- ✅ Edge cases (rapid popup opening/closing)
- ✅ Audio position preservation
- ✅ Error handling

### Running Tests
```bash
# Run comprehensive audio tests
node src/tests/run-audio-tests.js

# Run Jest tests (if Jest is configured)
npm test src/tests/audio-management.test.js
```

## Migration Guide

### For Existing Popups
1. Import `useAudioContextSafe` from `@/contexts/AudioContext`
2. Add popup registration in useEffect:
```jsx
const { addPopup, removePopup } = useAudioContextSafe();

useEffect(() => {
  const popupId = 'your-popup-id';
  addPopup(popupId);
  return () => removePopup(popupId);
}, [addPopup, removePopup]);
```

### For Custom Audio Controls
1. Replace direct audio manipulation with context methods
2. Use `toggleAudio()` instead of reducer actions
3. Check `shouldAudioPlay` for current state

## Performance Considerations

- **Memory Efficient**: Single audio element per viewer instance
- **CPU Optimized**: Minimal re-renders with memoization
- **Network Friendly**: Audio loaded once, no re-downloads
- **Battery Conscious**: Proper cleanup and event management

## Browser Compatibility

- ✅ Chrome 60+
- ✅ Firefox 55+  
- ✅ Safari 11+
- ✅ Edge 79+
- ⚠️ Autoplay policies respected (user interaction required)

## Future Enhancements

- [ ] Volume fade in/out during pause/resume
- [ ] Audio visualization during playback
- [ ] Multiple audio track support
- [ ] Spatial audio positioning
- [ ] Audio preloading optimization

## Troubleshooting

### Common Issues

1. **Audio not playing**: Check browser autoplay policies
2. **Audio restarts**: Verify AudioContextProvider wrapper
3. **Popup pause not working**: Ensure popup registration
4. **Multiple audio instances**: Check for duplicate providers

### Debug Tools
```javascript
// Enable audio debugging
localStorage.setItem('debug-audio', 'true');

// Check audio context state
console.log(useAudioContext());
```

## Conclusion

The enhanced audio management system provides a seamless, user-friendly experience with intelligent pause/resume functionality while maintaining continuous playback during scene transitions. The implementation follows React best practices and provides comprehensive error handling and testing coverage.
