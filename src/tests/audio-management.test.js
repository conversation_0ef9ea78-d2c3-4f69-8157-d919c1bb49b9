/**
 * Audio Management Tests
 * 
 * Tests for the enhanced audio system in the 360-degree viewer
 * covering continuous playback, popup pause/resume, and edge cases.
 */

import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { jest } from '@jest/globals';
import ThreeSixtyViewer from '../components/360s/360Viewer';
import { AudioContextProvider, useAudioContext } from '../contexts/AudioContext';
import AmbientAudio from '../components/360s/AmbientAudio';

// Mock HTML5 Audio
const mockAudio = {
  play: jest.fn(() => Promise.resolve()),
  pause: jest.fn(),
  load: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  currentTime: 0,
  duration: 100,
  paused: false,
  volume: 0.2,
  loop: true,
  crossOrigin: 'anonymous',
  preload: 'auto'
};

global.Audio = jest.fn(() => mockAudio);

// Mock fetch for 360 data
global.fetch = jest.fn(() =>
  Promise.resolve({
    json: () => Promise.resolve({
      success: true,
      data: [
        {
          _id: '1',
          name: 'test-scene',
          url: '/test-image.jpg',
          priority: 1,
          markerList: []
        }
      ]
    })
  })
);

describe('Audio Management System', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockAudio.paused = false;
    mockAudio.currentTime = 0;
  });

  describe('Continuous Audio Playback', () => {
    test('audio continues playing when switching between textures', async () => {
      const TestComponent = () => {
        const { registerAudioRef } = useAudioContext();
        const audioRef = { current: mockAudio };
        
        React.useEffect(() => {
          registerAudioRef(audioRef.current);
        }, [registerAudioRef]);

        return <div data-testid="test-component">Test</div>;
      };

      render(
        <AudioContextProvider>
          <TestComponent />
        </AudioContextProvider>
      );

      // Simulate texture switching
      act(() => {
        // Texture switch should not affect audio
      });

      await waitFor(() => {
        expect(mockAudio.pause).not.toHaveBeenCalled();
        expect(mockAudio.play).toHaveBeenCalled();
      });
    });

    test('audio maintains playback position during texture switches', async () => {
      mockAudio.currentTime = 45.5;

      render(
        <AudioContextProvider>
          <AmbientAudio volume={0.2} autoPlay={true} />
        </AudioContextProvider>
      );

      // Simulate texture change
      act(() => {
        // Component re-render should not reset currentTime
      });

      expect(mockAudio.currentTime).toBe(45.5);
    });
  });

  describe('Popup Audio Pause/Resume', () => {
    test('audio pauses when info video popup opens', async () => {
      const TestComponent = () => {
        const { addPopup, removePopup } = useAudioContext();
        
        return (
          <div>
            <button 
              onClick={() => addPopup('video-popup')}
              data-testid="open-video-popup"
            >
              Open Video
            </button>
            <button 
              onClick={() => removePopup('video-popup')}
              data-testid="close-video-popup"
            >
              Close Video
            </button>
          </div>
        );
      };

      render(
        <AudioContextProvider>
          <TestComponent />
        </AudioContextProvider>
      );

      // Open popup
      fireEvent.click(screen.getByTestId('open-video-popup'));

      await waitFor(() => {
        expect(mockAudio.pause).toHaveBeenCalled();
      });

      // Close popup
      fireEvent.click(screen.getByTestId('close-video-popup'));

      await waitFor(() => {
        expect(mockAudio.play).toHaveBeenCalled();
      });
    });

    test('audio pauses when info document popup opens', async () => {
      const TestComponent = () => {
        const { addPopup, removePopup } = useAudioContext();
        
        return (
          <div>
            <button 
              onClick={() => addPopup('doc-popup')}
              data-testid="open-doc-popup"
            >
              Open Document
            </button>
            <button 
              onClick={() => removePopup('doc-popup')}
              data-testid="close-doc-popup"
            >
              Close Document
            </button>
          </div>
        );
      };

      render(
        <AudioContextProvider>
          <TestComponent />
        </AudioContextProvider>
      );

      fireEvent.click(screen.getByTestId('open-doc-popup'));

      await waitFor(() => {
        expect(mockAudio.pause).toHaveBeenCalled();
      });
    });

    test('audio preserves playback position during pause/resume', async () => {
      mockAudio.currentTime = 30.2;

      const TestComponent = () => {
        const { addPopup, removePopup } = useAudioContext();
        
        return (
          <div>
            <button 
              onClick={() => addPopup('test-popup')}
              data-testid="open-popup"
            >
              Open
            </button>
            <button 
              onClick={() => removePopup('test-popup')}
              data-testid="close-popup"
            >
              Close
            </button>
          </div>
        );
      };

      render(
        <AudioContextProvider>
          <TestComponent />
        </AudioContextProvider>
      );

      // Open popup (pause)
      fireEvent.click(screen.getByTestId('open-popup'));
      
      await waitFor(() => {
        expect(mockAudio.pause).toHaveBeenCalled();
      });

      // Verify currentTime is preserved
      expect(mockAudio.currentTime).toBe(30.2);

      // Close popup (resume)
      fireEvent.click(screen.getByTestId('close-popup'));

      await waitFor(() => {
        expect(mockAudio.play).toHaveBeenCalled();
      });

      // currentTime should still be preserved
      expect(mockAudio.currentTime).toBe(30.2);
    });
  });

  describe('Multiple Popup Scenarios', () => {
    test('audio remains paused when multiple popups are open', async () => {
      const TestComponent = () => {
        const { addPopup, removePopup } = useAudioContext();
        
        return (
          <div>
            <button onClick={() => addPopup('popup1')} data-testid="open-popup1">Open 1</button>
            <button onClick={() => addPopup('popup2')} data-testid="open-popup2">Open 2</button>
            <button onClick={() => removePopup('popup1')} data-testid="close-popup1">Close 1</button>
            <button onClick={() => removePopup('popup2')} data-testid="close-popup2">Close 2</button>
          </div>
        );
      };

      render(
        <AudioContextProvider>
          <TestComponent />
        </AudioContextProvider>
      );

      // Open first popup
      fireEvent.click(screen.getByTestId('open-popup1'));
      await waitFor(() => expect(mockAudio.pause).toHaveBeenCalled());

      jest.clearMocks();

      // Open second popup
      fireEvent.click(screen.getByTestId('open-popup2'));
      
      // Audio should not be called again (already paused)
      expect(mockAudio.pause).not.toHaveBeenCalled();
      expect(mockAudio.play).not.toHaveBeenCalled();

      // Close first popup
      fireEvent.click(screen.getByTestId('close-popup1'));
      
      // Audio should still be paused (second popup still open)
      expect(mockAudio.play).not.toHaveBeenCalled();

      // Close second popup
      fireEvent.click(screen.getByTestId('close-popup2'));
      
      // Now audio should resume
      await waitFor(() => expect(mockAudio.play).toHaveBeenCalled());
    });
  });

  describe('User Audio Toggle', () => {
    test('user can disable audio completely', async () => {
      const TestComponent = () => {
        const { toggleAudio, isAudioEnabled } = useAudioContext();
        
        return (
          <div>
            <button onClick={toggleAudio} data-testid="toggle-audio">
              {isAudioEnabled ? 'Disable' : 'Enable'} Audio
            </button>
            <span data-testid="audio-status">
              {isAudioEnabled ? 'enabled' : 'disabled'}
            </span>
          </div>
        );
      };

      render(
        <AudioContextProvider>
          <TestComponent />
        </AudioContextProvider>
      );

      expect(screen.getByTestId('audio-status')).toHaveTextContent('enabled');

      fireEvent.click(screen.getByTestId('toggle-audio'));

      await waitFor(() => {
        expect(screen.getByTestId('audio-status')).toHaveTextContent('disabled');
        expect(mockAudio.pause).toHaveBeenCalled();
      });
    });
  });

  describe('Edge Cases', () => {
    test('handles rapid popup opening and closing', async () => {
      const TestComponent = () => {
        const { addPopup, removePopup } = useAudioContext();
        
        return (
          <button 
            onClick={() => {
              addPopup('rapid-popup');
              setTimeout(() => removePopup('rapid-popup'), 10);
            }}
            data-testid="rapid-toggle"
          >
            Rapid Toggle
          </button>
        );
      };

      render(
        <AudioContextProvider>
          <TestComponent />
        </AudioContextProvider>
      );

      // Rapid fire clicks
      for (let i = 0; i < 5; i++) {
        fireEvent.click(screen.getByTestId('rapid-toggle'));
      }

      // Should handle gracefully without errors
      await waitFor(() => {
        expect(mockAudio.pause).toHaveBeenCalled();
      });
    });

    test('handles audio loading errors gracefully', async () => {
      const errorAudio = {
        ...mockAudio,
        play: jest.fn(() => Promise.reject(new Error('Audio load failed')))
      };

      global.Audio = jest.fn(() => errorAudio);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      render(
        <AudioContextProvider>
          <AmbientAudio volume={0.2} autoPlay={true} />
        </AudioContextProvider>
      );

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalled();
      });

      consoleSpy.mockRestore();
    });
  });
});
