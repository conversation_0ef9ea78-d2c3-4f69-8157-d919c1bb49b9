'use client';

import React, { createContext, useContext, useRef, useState, useEffect } from 'react';

const GlobalAudioContext = createContext(null);

/**
 * GlobalAudioProvider - Persistent Audio Context for Next.js
 * 
 * This provider should be placed in _app.js to ensure audio persists
 * across all page navigation in the Next.js application.
 * 
 * Features:
 * - Persistent audio playback across route changes
 * - Global state management for audio controls
 * - Automatic handling of browser autoplay restrictions
 * - Volume and progress tracking
 * - Error handling and recovery
 */
export const GlobalAudioProvider = ({ children }) => {
  // useRef to hold the audio element - won't trigger re-renders
  const audioRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentTrack, setCurrentTrack] = useState({
    src: '/assets/Ambient_Music.mp4',
    title: 'Ambient Music',
    artist: 'Elephant Island'
  });
  const [volume, setVolume] = useState(0.4);
  const [progress, setProgress] = useState(0); // 0 to 1 for playback progress
  const [duration, setDuration] = useState(0);
  const [error, setError] = useState(null);
  const [autoPlay, setAutoPlay] = useState(true);
  const [loop, setLoop] = useState(true);

  // Initialize audio element when component mounts
  useEffect(() => {
    if (!audioRef.current) {
      audioRef.current = new Audio();
      audioRef.current.volume = volume;
      audioRef.current.loop = loop;
      audioRef.current.preload = 'auto';

      // Event listeners for updating state
      audioRef.current.onplay = () => {
        setIsPlaying(true);
        setError(null);
        console.log('🎵 Global audio started playing');
      };

      audioRef.current.onpause = () => {
        setIsPlaying(false);
        console.log('⏸️ Global audio paused');
      };

      audioRef.current.onended = () => {
        setIsPlaying(false);
        console.log('🔚 Global audio ended');
      };

      audioRef.current.ontimeupdate = () => {
        if (audioRef.current.duration) {
          setProgress(audioRef.current.currentTime / audioRef.current.duration);
        }
      };

      audioRef.current.onloadedmetadata = () => {
        setDuration(audioRef.current.duration);
        setIsLoaded(true);
        setError(null);
        console.log('✅ Global audio metadata loaded, duration:', audioRef.current.duration);
      };

      audioRef.current.onloadeddata = () => {
        setIsLoaded(true);
        setError(null);
        console.log('📥 Global audio data loaded');
      };

      audioRef.current.onerror = (e) => {
        const errorMsg = `Audio loading failed: ${e.target.error?.message || 'Unknown error'}`;
        setError(errorMsg);
        setIsLoaded(false);
        setIsPlaying(false);
        console.error('❌ Global audio error:', errorMsg);
      };

      audioRef.current.oncanplaythrough = () => {
        setIsLoaded(true);
        setError(null);
        console.log('🎯 Global audio can play through');
        
        // Auto-play if enabled
        if (autoPlay && !isPlaying) {
          playAudio();
        }
      };

      // Load the default track
      if (currentTrack?.src) {
        audioRef.current.src = currentTrack.src;
        audioRef.current.load();
      }
    }

    // Cleanup function
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
        audioRef.current.load();
      }
    };
  }, []); // Run only once on mount

  // Update audio element's volume when state changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  // Update audio element's loop when state changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.loop = loop;
    }
  }, [loop]);

  // Play audio function with autoplay restriction handling
  const playAudio = async () => {
    if (!audioRef.current || !isLoaded) {
      console.warn('Cannot play: audio not loaded');
      return;
    }

    try {
      await audioRef.current.play();
      setIsPlaying(true);
      setError(null);
      console.log('🎵 Global audio play successful');
    } catch (err) {
      console.warn('Autoplay prevented by browser policy:', err.message);
      setError('Click anywhere to enable audio playback');
      
      // Set up user interaction listener for autoplay
      const handleUserInteraction = async () => {
        try {
          if (audioRef.current && audioRef.current.paused) {
            await audioRef.current.play();
            setIsPlaying(true);
            setError(null);
            console.log('🎵 Global audio started after user interaction');
          }
        } catch (playErr) {
          console.error('Failed to play audio after interaction:', playErr);
          setError('Audio playback failed');
        }
        
        // Remove listeners after first interaction
        document.removeEventListener('click', handleUserInteraction);
        document.removeEventListener('keydown', handleUserInteraction);
        document.removeEventListener('touchstart', handleUserInteraction);
      };

      // Add interaction listeners
      document.addEventListener('click', handleUserInteraction);
      document.addEventListener('keydown', handleUserInteraction);
      document.addEventListener('touchstart', handleUserInteraction);
    }
  };

  const pauseAudio = () => {
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const togglePlayPause = async () => {
    if (!audioRef.current || !isLoaded) {
      console.warn('Cannot toggle: audio not loaded');
      return;
    }

    if (isPlaying) {
      pauseAudio();
    } else {
      await playAudio();
    }
  };

  const setPlaybackTime = (time) => {
    if (audioRef.current && !isNaN(audioRef.current.duration)) {
      audioRef.current.currentTime = Math.max(0, Math.min(duration, time));
    }
  };

  const changeTrack = (track) => {
    if (!audioRef.current) return;

    const wasPlaying = isPlaying;
    
    setCurrentTrack(track);
    audioRef.current.src = track.src;
    audioRef.current.load();
    
    // Resume playing if it was playing before
    if (wasPlaying) {
      audioRef.current.addEventListener('canplaythrough', () => {
        playAudio();
      }, { once: true });
    }
  };

  const value = {
    // State
    audioRef,
    isPlaying,
    isLoaded,
    currentTrack,
    volume,
    progress,
    duration,
    error,
    autoPlay,
    loop,
    
    // Controls
    playAudio,
    pauseAudio,
    togglePlayPause,
    setVolume,
    setPlaybackTime,
    changeTrack,
    setAutoPlay,
    setLoop,
    
    // Computed values
    currentTime: audioRef.current?.currentTime || 0,
    formattedCurrentTime: formatTime(audioRef.current?.currentTime || 0),
    formattedDuration: formatTime(duration),
  };

  return (
    <GlobalAudioContext.Provider value={value}>
      {children}
    </GlobalAudioContext.Provider>
  );
};

// Utility function to format time
const formatTime = (seconds) => {
  if (isNaN(seconds)) return '0:00';
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
};

// Custom hook to use the global audio context
export const useGlobalAudio = () => {
  const context = useContext(GlobalAudioContext);
  if (!context) {
    throw new Error('useGlobalAudio must be used within a GlobalAudioProvider');
  }
  return context;
};

// Safe hook that returns null if context is not available
export const useGlobalAudioSafe = () => {
  const context = useContext(GlobalAudioContext);
  return context || null;
};
