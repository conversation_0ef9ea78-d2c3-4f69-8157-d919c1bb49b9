/* Custom Rich Text Editor Styles */
.text-editor-container [contenteditable] {
  outline: none;
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

.text-editor-container [contenteditable]:empty::before {
  content: attr(data-placeholder);
  color: #999;
  font-style: italic;
  pointer-events: none;
  position: absolute;
}

.text-editor-container [contenteditable]:focus:empty::before {
  content: attr(data-placeholder);
  color: #ccc;
}

/* Toolbar button hover effects */
.text-editor-container button:hover {
  background-color: #f3f4f6 !important;
}

.text-editor-container button:active {
  background-color: #e5e7eb !important;
}

/* Format the content inside the editor */
.text-editor-container [contenteditable] p {
  margin: 0 0 1em 0;
}

.text-editor-container [contenteditable] p:last-child {
  margin-bottom: 0;
}

.text-editor-container [contenteditable] strong {
  font-weight: bold;
}

.text-editor-container [contenteditable] em {
  font-style: italic;
}

/* Preview section styling */
.formatted-content {
  font-family: inherit;
  line-height: 1.6;
}

.formatted-content p {
  margin-bottom: 1em;
}

.formatted-content p:last-child {
  margin-bottom: 0;
}

.formatted-content strong {
  font-weight: bold;
}

.formatted-content em {
  font-style: italic;
}

/* Ensure text selection works properly */
.text-editor-container [contenteditable] * {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

/* Selection highlighting */
.text-editor-container [contenteditable]::selection {
  background-color: #b3d4fc;
}

.text-editor-container [contenteditable] *::selection {
  background-color: #b3d4fc;
}

/* Font size classes for better control */
.text-editor-container .font-size-60 {
  font-size: 60px !important;
}

.text-editor-container .font-size-40 {
  font-size: 40px !important;
}

.text-editor-container .font-size-28 {
  font-size: 28px !important;
}

.text-editor-container .font-size-20 {
  font-size: 20px !important;
}

.ql-snow .ql-toolbar {
  border-bottom: 1px solid #d1d5db;
  border-top: none;
  border-left: none;
  border-right: none;
  border-radius: 0.375rem 0.375rem 0 0;
}

.ql-snow .ql-container {
  border-top: none;
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-radius: 0 0 0.375rem 0.375rem;
}

/* Improve focus states */
.ql-snow.ql-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 1px #3b82f6;
}

/* Loading state improvements */
.react-quill-loading {
  background: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.75rem;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Error state styling */
.react-quill-error {
  border-color: #ef4444;
  background-color: #fef2f2;
}

/* Suppress any browser deprecation warning popups */
.ql-editor::before {
  content: none !important;
}

/* Ensure proper z-index for dropdowns */
.ql-snow .ql-picker-options {
  z-index: 1000;
}

/* Font size support - Global styles for all contexts */
.ql-size-small {
  font-size: 0.75em !important;
}

.ql-size-large {
  font-size: 1.5em !important;
}

.ql-size-huge {
  font-size: 2.5em !important;
}

/* Ensure font sizes are preserved in editor content */
.ql-editor .ql-size-small {
  font-size: 0.75em !important;
}

.ql-editor .ql-size-large {
  font-size: 1.5em !important;
}

.ql-editor .ql-size-huge {
  font-size: 2.5em !important;
}

/* Font size support for display contexts (HtmlContentDisplay) */
.html-content-display .ql-size-small,
.quill-content .ql-size-small {
  font-size: 0.75em !important;
}

.html-content-display .ql-size-large,
.quill-content .ql-size-large {
  font-size: 1.5em !important;
}

.html-content-display .ql-size-huge,
.quill-content .ql-size-huge {
  font-size: 2.5em !important;
}

/* Ensure nested elements inherit font sizes properly */
.html-content-display span[class*="ql-size"],
.quill-content span[class*="ql-size"] {
  display: inline;
  line-height: inherit;
}

/* Support for paragraphs and other block elements with font sizes */
.html-content-display p .ql-size-small,
.html-content-display div .ql-size-small,
.quill-content p .ql-size-small,
.quill-content div .ql-size-small {
  font-size: 0.75em !important;
}

.html-content-display p .ql-size-large,
.html-content-display div .ql-size-large,
.quill-content p .ql-size-large,
.quill-content div .ql-size-large {
  font-size: 1.5em !important;
}

.html-content-display p .ql-size-huge,
.html-content-display div .ql-size-huge,
.quill-content p .ql-size-huge,
.quill-content div .ql-size-huge {
  font-size: 2.5em !important;
}

/* Enhanced support for BookingFormComponent context (black background, white text) */
.text-white .html-content-display .ql-size-small,
.text-white .quill-content .ql-size-small,
.booing-form .ql-size-small {
  font-size: 0.75em !important;
  color: inherit;
}

.text-white .html-content-display .ql-size-large,
.text-white .quill-content .ql-size-large,
.booing-form .ql-size-large {
  font-size: 1.5em !important;
  color: inherit;
}

.text-white .html-content-display .ql-size-huge,
.text-white .quill-content .ql-size-huge,
.booing-form .ql-size-huge {
  font-size: 2.5em !important;
  color: inherit;
}

/* Support for deeply nested elements in booking context */
.text-white .html-content-display p .ql-size-small,
.text-white .html-content-display div .ql-size-small,
.text-white .html-content-display span .ql-size-small,
.booing-form p .ql-size-small,
.booing-form div .ql-size-small,
.booing-form span .ql-size-small {
  font-size: 0.75em !important;
  color: inherit;
}

.text-white .html-content-display p .ql-size-large,
.text-white .html-content-display div .ql-size-large,
.text-white .html-content-display span .ql-size-large,
.booing-form p .ql-size-large,
.booing-form div .ql-size-large,
.booing-form span .ql-size-large {
  font-size: 1.5em !important;
  color: inherit;
}

.text-white .html-content-display p .ql-size-huge,
.text-white .html-content-display div .ql-size-huge,
.text-white .html-content-display span .ql-size-huge,
.booing-form p .ql-size-huge,
.booing-form div .ql-size-huge,
.booing-form span .ql-size-huge {
  font-size: 2.5em !important;
  color: inherit;
}

/* Font family support for TextEditor component */
.ql-font-Arial {
  font-family: Arial, sans-serif !important;
}

.ql-font-Times-New-Roman {
  font-family: 'Times New Roman', serif !important;
}

.ql-font-Helvetica {
  font-family: Helvetica, sans-serif !important;
}

.ql-font-Georgia {
  font-family: Georgia, serif !important;
}

.ql-font-Verdana {
  font-family: Verdana, sans-serif !important;
}

/* Font picker dropdown styling */
.ql-font .ql-picker-label::before,
.ql-font .ql-picker-item::before {
  content: 'Font Family' !important;
}

.ql-picker-label[data-value="Arial"]::before,
.ql-picker-item[data-value="Arial"]::before {
  content: 'Arial' !important;
  font-family: Arial, sans-serif !important;
}

.ql-picker-label[data-value="Times-New-Roman"]::before,
.ql-picker-item[data-value="Times-New-Roman"]::before {
  content: 'Times New Roman' !important;
  font-family: 'Times New Roman', serif !important;
}

.ql-picker-label[data-value="Helvetica"]::before,
.ql-picker-item[data-value="Helvetica"]::before {
  content: 'Helvetica' !important;
  font-family: Helvetica, sans-serif !important;
}

.ql-picker-label[data-value="Georgia"]::before,
.ql-picker-item[data-value="Georgia"]::before {
  content: 'Georgia' !important;
  font-family: Georgia, serif !important;
}

.ql-picker-label[data-value="Verdana"]::before,
.ql-picker-item[data-value="Verdana"]::before {
  content: 'Verdana' !important;
  font-family: Verdana, sans-serif !important;
}

/* Ensure font families are preserved in display contexts */
.html-content-display .ql-font-Arial,
.quill-content .ql-font-Arial,
.formatted-content .ql-font-Arial {
  font-family: Arial, sans-serif !important;
}

.html-content-display .ql-font-Times-New-Roman,
.quill-content .ql-font-Times-New-Roman,
.formatted-content .ql-font-Times-New-Roman {
  font-family: 'Times New Roman', serif !important;
}

.html-content-display .ql-font-Helvetica,
.quill-content .ql-font-Helvetica,
.formatted-content .ql-font-Helvetica {
  font-family: Helvetica, sans-serif !important;
}

.html-content-display .ql-font-Georgia,
.quill-content .ql-font-Georgia,
.formatted-content .ql-font-Georgia {
  font-family: Georgia, serif !important;
}

.html-content-display .ql-font-Verdana,
.quill-content .ql-font-Verdana,
.formatted-content .ql-font-Verdana {
  font-family: Verdana, sans-serif !important;
}

/* Additional font dropdown fixes */
.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: 'Sans Serif' !important;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Arial"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Arial"]::before {
  content: 'Arial' !important;
  font-family: Arial, sans-serif !important;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Times-New-Roman"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Times-New-Roman"]::before {
  content: 'Times New Roman' !important;
  font-family: 'Times New Roman', serif !important;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Helvetica"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Helvetica"]::before {
  content: 'Helvetica' !important;
  font-family: Helvetica, sans-serif !important;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Georgia"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Georgia"]::before {
  content: 'Georgia' !important;
  font-family: Georgia, serif !important;
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="Verdana"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="Verdana"]::before {
  content: 'Verdana' !important;
  font-family: Verdana, sans-serif !important;
}

/* Debug styles to ensure font classes are working */
.ql-editor .ql-font-Arial,
.ql-editor .ql-font-Times-New-Roman,
.ql-editor .ql-font-Helvetica,
.ql-editor .ql-font-Georgia,
.ql-editor .ql-font-Verdana {
  border-left: 2px solid transparent;
}

/* Make sure font dropdown is visible */
.ql-snow .ql-picker.ql-font {
  width: 108px;
}

.ql-snow .ql-picker.ql-font .ql-picker-label {
  padding-left: 8px;
  padding-right: 8px;
}

/* Ensure dropdown options are visible */
.ql-snow .ql-picker-options {
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  max-height: 200px;
  overflow-y: auto;
}

.ql-snow .ql-picker-item {
  padding: 5px 12px;
  cursor: pointer;
}

.ql-snow .ql-picker-item:hover {
  background-color: #f0f0f0;
}

/* Ensure editor is interactive and properly styled */
.ql-container {
  font-size: 14px;
  height: auto;
}

.ql-editor {
  box-sizing: border-box;
  height: 100%;
  outline: none;
  overflow-y: auto;
  padding: 12px 15px;
  tab-size: 4;
  -moz-tab-size: 4;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.ql-editor > * {
  cursor: text;
}

.ql-editor p,
.ql-editor ol,
.ql-editor ul,
.ql-editor pre,
.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  margin: 0;
  padding: 0;
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}

/* Ensure text input is visible */
.ql-editor.ql-blank::before {
  color: #aaa;
  content: attr(data-placeholder);
  font-style: italic;
  left: 15px;
  pointer-events: none;
  position: absolute;
  right: 15px;
}
