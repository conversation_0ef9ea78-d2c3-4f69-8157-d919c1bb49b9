'use client';

/**
 * Audio Preloading and Caching System
 * 
 * Preloads and caches audio files during website initialization
 * to ensure immediate availability when needed for playback.
 */

// Audio cache to store preloaded audio files
const audioCache = new Map();
const loadingPromises = new Map();

// Configuration for audio preloading
const AUDIO_CONFIG = {
  maxRetries: 3,
  retryDelay: 1000,
  requestTimeout: 30000, // 30 seconds for audio files
  cacheTimeout: 30 * 60 * 1000, // 30 minutes cache
  preloadOnInit: true,
  enableServiceWorkerCache: true
};

// Audio files to preload
const AUDIO_ASSETS = {
  ambient: '/assets/Ambient_Music.mp4',
  // Add more audio files here as needed
  // backgroundMusic: '/assets/Background_Music.mp3',
  // uiSounds: '/assets/UI_Sounds.mp3'
};

/**
 * Resolve audio URL for production compatibility
 */
function resolveAudioUrl(src) {
  if (!src) return null;
  
  // Handle absolute URLs
  if (src.startsWith('http://') || src.startsWith('https://')) {
    return src;
  }
  
  // For production, route through asset serving API
  if (process.env.NODE_ENV === 'production') {
    const filename = src.split('/').pop();
    return `/api/assets/serve/${filename}`;
  }
  
  // In development, ensure proper path format
  return src.startsWith('/') ? src : `/${src}`;
}

/**
 * Load audio with timeout and retry logic
 */
async function loadAudioWithTimeout(src, timeout = AUDIO_CONFIG.requestTimeout) {
  return new Promise((resolve, reject) => {
    const audio = new Audio();
    let timeoutId;
    let isResolved = false;

    const cleanup = () => {
      if (timeoutId) clearTimeout(timeoutId);
      audio.removeEventListener('canplaythrough', onLoad);
      audio.removeEventListener('loadeddata', onLoad);
      audio.removeEventListener('error', onError);
    };

    const onLoad = () => {
      if (isResolved) return;
      isResolved = true;
      cleanup();
      console.log('✅ Audio preloaded successfully:', src);
      resolve(audio);
    };

    const onError = (error) => {
      if (isResolved) return;
      isResolved = true;
      cleanup();
      reject(new Error(`Failed to load audio: ${error.message || 'Unknown error'}`));
    };

    // Set up timeout
    timeoutId = setTimeout(() => {
      if (isResolved) return;
      isResolved = true;
      cleanup();
      reject(new Error(`Audio loading timeout after ${timeout}ms`));
    }, timeout);

    // Configure audio element
    audio.crossOrigin = 'anonymous';
    audio.preload = 'auto';
    
    // Add event listeners
    audio.addEventListener('canplaythrough', onLoad);
    audio.addEventListener('loadeddata', onLoad);
    audio.addEventListener('error', onError);

    // Start loading
    audio.src = src;
    audio.load();
  });
}

/**
 * Preload audio with retry logic and caching
 */
export async function preloadAudio(src, options = {}) {
  const {
    maxRetries = AUDIO_CONFIG.maxRetries,
    retryDelay = AUDIO_CONFIG.retryDelay,
    timeout = AUDIO_CONFIG.requestTimeout,
    id = 'unknown'
  } = options;

  // Resolve URL
  const resolvedSrc = resolveAudioUrl(src);
  if (!resolvedSrc) {
    throw new Error(`Invalid audio URL: ${src}`);
  }

  // Check cache first
  const cacheKey = resolvedSrc;
  const cached = audioCache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < AUDIO_CONFIG.cacheTimeout) {
    console.log('🎵 Audio served from cache:', resolvedSrc);
    return cached.audio;
  }

  // Check if already loading
  if (loadingPromises.has(cacheKey)) {
    console.log('⏳ Audio already loading, waiting...:', resolvedSrc);
    return loadingPromises.get(cacheKey);
  }

  // Create loading promise with retry logic
  const loadingPromise = (async () => {
    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📥 Loading audio (attempt ${attempt + 1}/${maxRetries + 1}):`, resolvedSrc);
        
        const audio = await loadAudioWithTimeout(resolvedSrc, timeout);
        
        // Cache successful result
        audioCache.set(cacheKey, {
          audio,
          timestamp: Date.now(),
          src: resolvedSrc
        });
        
        console.log('✅ Audio cached successfully:', resolvedSrc);
        return audio;
        
      } catch (error) {
        lastError = error;
        console.warn(`❌ Audio load attempt ${attempt + 1} failed:`, error.message);
        
        if (attempt < maxRetries) {
          const delay = retryDelay * Math.pow(2, attempt); // Exponential backoff
          console.log(`⏱️  Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    throw new Error(`Failed to load audio after ${maxRetries + 1} attempts: ${lastError?.message}`);
  })();

  loadingPromises.set(cacheKey, loadingPromise);
  
  try {
    const result = await loadingPromise;
    return result;
  } finally {
    loadingPromises.delete(cacheKey);
  }
}

/**
 * Get cached audio if available
 */
export function getCachedAudio(src) {
  const resolvedSrc = resolveAudioUrl(src);
  if (!resolvedSrc) return null;
  
  const cached = audioCache.get(resolvedSrc);
  if (cached && Date.now() - cached.timestamp < AUDIO_CONFIG.cacheTimeout) {
    return cached.audio;
  }
  
  return null;
}

/**
 * Preload all essential audio files
 */
export async function preloadEssentialAudio() {
  console.log('🎵 Starting audio preloading...');
  
  const preloadPromises = Object.entries(AUDIO_ASSETS).map(async ([key, src]) => {
    try {
      await preloadAudio(src, { id: key });
      console.log(`✅ Preloaded ${key} audio:`, src);
    } catch (error) {
      console.error(`❌ Failed to preload ${key} audio:`, error.message);
      // Don't throw - continue with other audio files
    }
  });
  
  // Wait for all preloads to complete (or fail)
  await Promise.allSettled(preloadPromises);
  console.log('🎵 Audio preloading completed');
}

/**
 * Clear audio cache
 */
export function clearAudioCache(src = null) {
  if (src) {
    const resolvedSrc = resolveAudioUrl(src);
    if (resolvedSrc && audioCache.has(resolvedSrc)) {
      audioCache.delete(resolvedSrc);
      console.log('🗑️  Cleared audio cache for:', resolvedSrc);
    }
  } else {
    audioCache.clear();
    console.log('🗑️  Cleared all audio cache');
  }
}

/**
 * Get cache statistics
 */
export function getAudioCacheStats() {
  const stats = {
    totalCached: audioCache.size,
    cacheSize: 0,
    entries: []
  };
  
  audioCache.forEach((cached, src) => {
    const age = Date.now() - cached.timestamp;
    stats.entries.push({
      src,
      age: Math.round(age / 1000), // seconds
      expired: age > AUDIO_CONFIG.cacheTimeout
    });
  });
  
  return stats;
}

/**
 * Enhanced preload with service worker integration
 */
export async function preloadEssentialAudioWithServiceWorker() {
  console.log('🎵 Starting enhanced audio preloading with service worker...');

  try {
    // First, try to initialize service worker
    const { initializeAudioServiceWorker, cacheAudioFilesViaServiceWorker } = await import('./audio-service-worker');

    const swInitialized = await initializeAudioServiceWorker();

    if (swInitialized) {
      console.log('🔧 Service worker initialized, using persistent caching');

      // Cache via service worker for persistence
      const audioUrls = Object.values(AUDIO_ASSETS);
      await cacheAudioFilesViaServiceWorker(audioUrls);
    }

    // Also preload in memory for immediate access
    await preloadEssentialAudio();

    console.log('✅ Enhanced audio preloading completed');

  } catch (error) {
    console.warn('⚠️ Service worker caching failed, falling back to memory cache:', error);

    // Fallback to regular preloading
    await preloadEssentialAudio();
  }
}

/**
 * Initialize audio preloading when module loads
 */
if (typeof window !== 'undefined' && AUDIO_CONFIG.preloadOnInit) {
  // Wait for initial page load to complete
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      // Delay preloading to not interfere with critical resources
      setTimeout(preloadEssentialAudioWithServiceWorker, 1500);
    });
  } else {
    // Document already loaded
    setTimeout(preloadEssentialAudioWithServiceWorker, 1500);
  }
}

// Export configuration for external access
export { AUDIO_CONFIG, AUDIO_ASSETS };
