'use client';

/**
 * Audio Service Worker Registration and Management
 * 
 * Handles registration and communication with the audio cache service worker
 * for persistent audio file caching across browser sessions.
 */

let serviceWorkerRegistration = null;
let isServiceWorkerSupported = false;

// Check service worker support
if (typeof window !== 'undefined') {
  isServiceWorkerSupported = 'serviceWorker' in navigator;
}

/**
 * Register the audio cache service worker
 */
export async function registerAudioServiceWorker() {
  if (!isServiceWorkerSupported) {
    console.log('⚠️ Service Worker not supported in this browser');
    return null;
  }

  try {
    console.log('🔧 Registering Audio Cache Service Worker...');
    
    const registration = await navigator.serviceWorker.register('/audio-cache-sw.js', {
      scope: '/',
      updateViaCache: 'none'
    });

    serviceWorkerRegistration = registration;

    // Handle service worker updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;
      console.log('🔄 New service worker installing...');
      
      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          console.log('✅ New service worker installed, ready to use');
        }
      });
    });

    // Listen for service worker messages
    navigator.serviceWorker.addEventListener('message', handleServiceWorkerMessage);

    console.log('✅ Audio Cache Service Worker registered successfully');
    return registration;

  } catch (error) {
    console.error('❌ Service Worker registration failed:', error);
    return null;
  }
}

/**
 * Handle messages from service worker
 */
function handleServiceWorkerMessage(event) {
  const { type, data } = event.data;
  
  switch (type) {
    case 'AUDIO_CACHED':
      console.log('🎵 Audio file cached by service worker:', data.url);
      break;
      
    case 'AUDIO_CACHE_ERROR':
      console.error('❌ Service worker audio cache error:', data.error);
      break;
      
    default:
      console.log('📨 Service worker message:', type, data);
  }
}

/**
 * Send message to service worker
 */
async function sendMessageToServiceWorker(message) {
  if (!serviceWorkerRegistration || !serviceWorkerRegistration.active) {
    throw new Error('Service worker not available');
  }

  return new Promise((resolve, reject) => {
    const messageChannel = new MessageChannel();
    
    messageChannel.port1.onmessage = (event) => {
      const { type, error, ...data } = event.data;
      
      if (error) {
        reject(new Error(error));
      } else {
        resolve({ type, ...data });
      }
    };

    serviceWorkerRegistration.active.postMessage(message, [messageChannel.port2]);
  });
}

/**
 * Cache audio files via service worker
 */
export async function cacheAudioFilesViaServiceWorker(audioUrls) {
  try {
    console.log('📥 Requesting service worker to cache audio files:', audioUrls);
    
    const response = await sendMessageToServiceWorker({
      type: 'CACHE_AUDIO',
      data: audioUrls
    });

    console.log('✅ Service worker audio caching response:', response);
    return response.results;

  } catch (error) {
    console.error('❌ Failed to cache audio via service worker:', error);
    throw error;
  }
}

/**
 * Clear audio cache via service worker
 */
export async function clearAudioCacheViaServiceWorker() {
  try {
    console.log('🗑️ Requesting service worker to clear audio cache');
    
    await sendMessageToServiceWorker({
      type: 'CLEAR_AUDIO_CACHE'
    });

    console.log('✅ Audio cache cleared via service worker');

  } catch (error) {
    console.error('❌ Failed to clear audio cache via service worker:', error);
    throw error;
  }
}

/**
 * Get cache status from service worker
 */
export async function getAudioCacheStatusFromServiceWorker() {
  try {
    const response = await sendMessageToServiceWorker({
      type: 'GET_CACHE_STATUS'
    });

    return response.status;

  } catch (error) {
    console.error('❌ Failed to get cache status from service worker:', error);
    throw error;
  }
}

/**
 * Initialize audio service worker on app load
 */
export async function initializeAudioServiceWorker() {
  if (!isServiceWorkerSupported) {
    console.log('⚠️ Skipping service worker initialization - not supported');
    return false;
  }

  try {
    // Register service worker
    const registration = await registerAudioServiceWorker();
    
    if (!registration) {
      return false;
    }

    // Wait for service worker to be ready
    await navigator.serviceWorker.ready;
    
    console.log('🚀 Audio Service Worker initialized and ready');
    return true;

  } catch (error) {
    console.error('❌ Audio Service Worker initialization failed:', error);
    return false;
  }
}

/**
 * Check if service worker is available and active
 */
export function isAudioServiceWorkerActive() {
  return !!(
    isServiceWorkerSupported &&
    serviceWorkerRegistration &&
    serviceWorkerRegistration.active
  );
}

/**
 * Auto-initialize service worker when module loads
 */
if (typeof window !== 'undefined') {
  // Initialize after a short delay to not block initial page load
  setTimeout(() => {
    initializeAudioServiceWorker().catch(error => {
      console.error('❌ Auto-initialization of Audio Service Worker failed:', error);
    });
  }, 2000);
}

export { isServiceWorkerSupported };
