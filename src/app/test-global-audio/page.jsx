'use client';

import { useGlobalAudio } from '@/contexts/GlobalAudioContext';
import AudioControl, { AudioStatusIndicator } from '@/components/AudioControl';
import Link from 'next/link';

export default function TestGlobalAudioPage() {
  const { 
    isPlaying, 
    isLoaded, 
    currentTrack, 
    volume, 
    progress, 
    duration,
    togglePlayPause,
    setVolume,
    formattedCurrentTime,
    formattedDuration
  } = useGlobalAudio();

  return (
    <div className="min-h-screen bg-gray-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8">
          🎵 Global Audio Test Page
        </h1>
        
        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4">Audio Status</h2>
          <AudioStatusIndicator className="mb-4" />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>Track:</strong> {currentTrack?.title || 'None'}
            </div>
            <div>
              <strong>Status:</strong> {isLoaded ? (isPlaying ? 'Playing' : 'Paused') : 'Loading'}
            </div>
            <div>
              <strong>Volume:</strong> {Math.round(volume * 100)}%
            </div>
            <div>
              <strong>Progress:</strong> {formattedCurrentTime} / {formattedDuration}
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4">Audio Controls</h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium mb-2">Button Variant</h3>
              <AudioControl variant="button" size="medium" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Toggle Variant</h3>
              <AudioControl variant="toggle" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Icon Only Variants</h3>
              <div className="flex space-x-4">
                <AudioControl variant="icon-only" size="small" />
                <AudioControl variant="icon-only" size="medium" />
                <AudioControl variant="icon-only" size="large" />
              </div>
            </div>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4">Volume Control</h2>
          <div className="flex items-center space-x-4">
            <span>Volume:</span>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
              className="flex-1 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer accent-blue-500"
            />
            <span className="w-12">{Math.round(volume * 100)}%</span>
          </div>
        </div>

        <div className="bg-gray-800 rounded-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4">Test Navigation</h2>
          <p className="mb-4 text-gray-300">
            The audio should continue playing when you navigate between these pages:
          </p>
          <div className="flex flex-wrap gap-4">
            <Link 
              href="/" 
              className="px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded-lg transition-colors"
            >
              Home
            </Link>
            <Link 
              href="/360s" 
              className="px-4 py-2 bg-green-500 hover:bg-green-600 rounded-lg transition-colors"
            >
              360° Viewer
            </Link>
            <Link 
              href="/test-global-audio" 
              className="px-4 py-2 bg-purple-500 hover:bg-purple-600 rounded-lg transition-colors"
            >
              This Page (Refresh Test)
            </Link>
          </div>
        </div>

        <div className="bg-blue-900 rounded-lg p-6">
          <h2 className="text-2xl font-semibold mb-4">How It Works</h2>
          <div className="space-y-3 text-sm text-blue-100">
            <p>
              <strong>Global Audio Context:</strong> The audio player is managed by a global context 
              that persists across all page navigation in the Next.js app.
            </p>
            <p>
              <strong>Layout Integration:</strong> The GlobalAudioProvider is wrapped around the entire 
              app in layout.js, and the GlobalAudioPlayer component is rendered at the app level.
            </p>
            <p>
              <strong>Persistent Playback:</strong> Because the audio player exists at the layout level, 
              it continues playing even when individual pages are unmounted and remounted during navigation.
            </p>
            <p>
              <strong>Control from Anywhere:</strong> Any page can control the global audio using the 
              useGlobalAudio hook or the AudioControl component.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
