import React, { Suspense } from 'react'
import _360Navbar from '@/components/360s/_360Navbar';
import PopupWrapper from '@/components/menu-popup/PopupWrapper';
import MenuPopupWrapper from '@/components/menu-popup/MenuPopupWrapper';
import Navbar from '@/components/Navbar';
import NavbarLandingPage from '@/components/NavbarLandingPage';
import NavbarWrapper from '@/components/NavbarWrapper';
import LoadingComponent from '@/components/SpinerComponent';

export default function layout({children,searchParams}) {
  // console.log(searchParams)
  const isLandingPage=searchParams?.id=='New_entrance_360_002'
  return (
    <div className='flex flex-grow w-full h-svh overflow-hidden'>
      <Suspense fallback={null}>
        {/* {isLandingPage ? <NavbarLandingPage/> : <_360Navbar/>} */}
        {/* <NavbarWrapper/> */}
        <PopupWrapper/>
        <MenuPopupWrapper/>
      </Suspense>
      {children}
    </div>
  )
}
