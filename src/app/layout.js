'use client';

import './globals.css';
import Link from 'next/link';
import Script from 'next/script';
import { Suspense } from 'react';
import { ExperienceContextProvider } from '@/contexts/useContextExperience';
import BookingWrapper from '@/components/menu-popup/BookingWrapper';
import Footer from '@/components/Footer';
import Socials from '@/components/Socials';
import AudioPreloader from '@/components/AudioPreloader';
import { GlobalAudioProvider } from '@/contexts/GlobalAudioContext';
import GlobalAudioPlayer from '@/components/GlobalAudioPlayer';

export default function RootLayout({ children }) {
  const user=''
  const link=['admin/dashboard','beta'];
  return (
    <html lang="en h-screen">
      <body className="font-trasandina bg-black antialiased relative flex-grow">
        <Script
          src="/suppress-quill-warnings.js"
          strategy="beforeInteractive"
          priority
        />
        <div className="site-links flex absolute w-fit h-fit flex-col-reverse items-end z-50 bottom-16 right-0 p-4 text-teal-500 underline-offset-1 capitalize gap-2">
          {link.map((i,index) =>
            {i=='admin/dashboard' && user?.role?.isAdmin 
              ? <Link key={index} href={`/${i}`}>{i}</Link> :
            i=='beta' 
               ? <Link key={index} href={`/${i}`}>{i}</Link> : null
            }
          )}
        </div>
        <GlobalAudioProvider>
          <ExperienceContextProvider>
            {/* Audio Preloader - starts immediately on app load */}
            <AudioPreloader
              showProgress={process.env.NODE_ENV === 'development'}
              onPreloadComplete={(stats) => console.log('🎵 Audio preload complete:', stats)}
              onPreloadError={(error) => console.error('🎵 Audio preload error:', error)}
            />

            <div className='flex-grow w-full h-svh overflow-hidden'>
              {children}
            </div>
            <Suspense fallback={null}>
              <BookingWrapper/>
            </Suspense>
            <Socials/>
            <Footer/>

            {/* Global Audio Player - persists across all pages */}
            <GlobalAudioPlayer
              position="bottom-right"
              showControls={true}
            />
          </ExperienceContextProvider>
        </GlobalAudioProvider>
      </body>
    </html>
  );
}
