import { NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import { Page } from '@/models/Page';
import mongoose from 'mongoose';

// Development fallback data
const FALLBACK_PAGES_DATA = {
  island: {
    title: 'Welcome to The Island',
    image: '',
    body1: 'Discover the beauty and wonder of our pristine island paradise.',
    additionalContent: []
  },
  experiences: {
    title: 'Unforgettable Experiences',
    image: '',
    body1: 'Create memories that will last a lifetime with our curated experiences.',
    additionalContent: []
  },
  testimonials: {
    testimonials: []
  },
  locationAndcontacts: {
    title: 'Location & Contact',
    image: '',
    body1: 'Find us and get in touch for your perfect island getaway.',
    additionalContent: []
  },
  booking: {
    details: 'Enter booking information and details here. This section allows you to manage booking-related content for your website.'
  }
};

// GET - Fetch specific page section
export async function GET(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;

    // Get the pages document
    let pages = await Page.findOne().lean();

    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne().lean();
    }

    // Ensure booking field exists (for backward compatibility)
    if (!pages.booking) {
      pages.booking = {
        details: 'Enter booking information and details here. This section allows you to manage booking-related content for your website.'
      };
    }

    // Validate section name
    const validSections = ['island', 'experiences', 'testimonials', 'locationAndcontacts', 'booking'];
    if (!validSections.includes(id)) {
      return NextResponse.json(
        { success: false, message: 'Invalid section name' },
        { status: 400 }
      );
    }

    const sectionData = pages[id];

    if (!sectionData) {
      return NextResponse.json(
        { success: false, message: 'Section not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: sectionData,
      section: id
    });

  } catch (error) {
    console.error('Error fetching page section:', error);

    // Development fallback when MongoDB is not available
    if (process.env.NODE_ENV === 'development') {
      const { id } = await params;

      // Validate section name
      const validSections = ['island', 'experiences', 'testimonials', 'locationAndcontacts', 'booking'];
      if (!validSections.includes(id)) {
        return NextResponse.json(
          { success: false, message: 'Invalid section name' },
          { status: 400 }
        );
      }

      const sectionData = FALLBACK_PAGES_DATA[id];
      if (!sectionData) {
        return NextResponse.json(
          { success: false, message: 'Section not found' },
          { status: 404 }
        );
      }

      console.log(`MongoDB unavailable, using fallback data for section: ${id}`);
      return NextResponse.json({
        success: true,
        data: sectionData,
        section: id,
        fallback: true
      });
    }

    return NextResponse.json(
      { success: false, message: 'Failed to fetch page section', error: error.message },
      { status: 500 }
    );
  }
}

// PUT - Update specific page section (complete replacement)
export async function PUT(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();

    // Validate section name
    const validSections = ['island', 'experiences', 'testimonials', 'locationAndcontacts', 'booking'];
    if (!validSections.includes(id)) {
      return NextResponse.json(
        { success: false, message: 'Invalid section name' },
        { status: 400 }
      );
    }

    // Get or create the pages document
    let pages = await Page.findOne();
    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Ensure booking field exists (for backward compatibility)
    if (id === 'booking' && !pages.booking) {
      pages.booking = {
        details: 'Enter booking information and details here. This section allows you to manage booking-related content for your website.'
      };
    }

    // Update the specific section
    pages[id] = body;
    const updatedPages = await pages.save();

    return NextResponse.json({
      success: true,
      message: `${id} section updated successfully`,
      data: updatedPages[id]
    });

  } catch (error) {
    console.error('Error updating page section:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update page section', error: error.message },
      { status: 500 }
    );
  }
}

// PATCH - Partial update specific page section
export async function PATCH(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;
    const body = await request.json();

    // Validate section name
    const validSections = ['island', 'experiences', 'testimonials', 'locationAndcontacts', 'booking'];
    if (!validSections.includes(id)) {
      return NextResponse.json(
        { success: false, message: 'Invalid section name' },
        { status: 400 }
      );
    }

    // Get or create the pages document
    let pages = await Page.findOne();
    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Ensure booking field exists (for backward compatibility)
    if (id === 'booking' && !pages.booking) {
      pages.booking = {
        details: 'Enter booking information and details here. This section allows you to manage booking-related content for your website.'
      };
    }

    // Handle specific operations for testimonials
    if (body.operation && id === 'testimonials') {
      switch (body.operation) {
        case 'addTestimonial':
          if (body.testimonial) {
            // Check for unique name
            const existingNames = pages.testimonials.testimonials.map(t => t.name.toLowerCase());
            if (!existingNames.includes(body.testimonial.name.toLowerCase())) {
              pages.testimonials.testimonials.push(body.testimonial);
            } else {
              return NextResponse.json(
                { success: false, message: 'Testimonial name must be unique' },
                { status: 400 }
              );
            }
          }
          break;

        case 'updateTestimonial':
          if (body.testimonialId && body.testimonial) {
            const testimonialIndex = pages.testimonials.testimonials.findIndex(
              testimonial => testimonial._id.toString() === body.testimonialId
            );
            if (testimonialIndex !== -1) {
              // Check for unique name (excluding current testimonial)
              const existingNames = pages.testimonials.testimonials
                .filter((_, index) => index !== testimonialIndex)
                .map(t => t.name.toLowerCase());

              if (!existingNames.includes(body.testimonial.name.toLowerCase())) {
                Object.assign(pages.testimonials.testimonials[testimonialIndex], body.testimonial);
              } else {
                return NextResponse.json(
                  { success: false, message: 'Testimonial name must be unique' },
                  { status: 400 }
                );
              }
            }
          }
          break;

        case 'removeTestimonial':
          if (body.testimonialId) {
            pages.testimonials.testimonials = pages.testimonials.testimonials.filter(
              testimonial => testimonial._id.toString() !== body.testimonialId
            );
          }
          break;

        default:
          return NextResponse.json(
            { success: false, message: 'Invalid operation' },
            { status: 400 }
          );
      }
    } else {
      // Regular partial update - merge with existing section data
      if (pages[id]) {
        Object.assign(pages[id], body);
      } else {
        pages[id] = body;
      }
    }

    const updatedPages = await pages.save();

    return NextResponse.json({
      success: true,
      message: `${id} section updated successfully`,
      data: updatedPages[id]
    });

  } catch (error) {
    console.error('Error updating page section:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to update page section', error: error.message },
      { status: 500 }
    );
  }
}

// DELETE - Reset specific page section to default
export async function DELETE(request, { params }) {
  try {
    await connectDB();

    const { id } = await params;

    // Validate section name
    const validSections = ['island', 'experiences', 'testimonials', 'locationAndcontacts', 'booking'];
    if (!validSections.includes(id)) {
      return NextResponse.json(
        { success: false, message: 'Invalid section name' },
        { status: 400 }
      );
    }

    // Get or create the pages document
    let pages = await Page.findOne();
    if (!pages) {
      await Page.initializeDefaultPages();
      pages = await Page.findOne();
    }

    // Ensure booking field exists (for backward compatibility)
    if (id === 'booking' && !pages.booking) {
      pages.booking = {
        details: 'Enter booking information and details here. This section allows you to manage booking-related content for your website.'
      };
    }

    // Reset the specific section to default values
    const defaultValues = {
      island: {
        title: 'Welcome to The Island',
        body1: 'Discover the beauty and wonder of our pristine island paradise.',
        body2: 'Experience the untouched natural beauty and unique wildlife that makes our island special.'
      },
      experiences: {
        title: 'Unforgettable Experiences',
        body1: 'Create memories that will last a lifetime with our curated experiences.',
        body2: 'From wildlife encounters to cultural immersion, every moment is designed to inspire.'
      },
      testimonials: {
        testimonials: []
      },
      locationAndcontacts: {
        title: 'Contact Information',
        body: 'Get in touch with us for more information about Elephant Island Lodge.',
        details: 'Additional contact details and information about our location and services.'
      },
      booking: {
        details: 'Enter booking information and details here. This section allows you to manage booking-related content for your website.'
      }
    };

    pages[id] = defaultValues[id];
    const updatedPages = await pages.save();

    return NextResponse.json({
      success: true,
      message: `${id} section reset to default values`,
      data: updatedPages[id]
    });

  } catch (error) {
    console.error('Error resetting page section:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to reset page section', error: error.message },
      { status: 500 }
    );
  }
}
