# TextEditor Component

A React component that provides rich text editing capabilities using Quill.js with real-time formatted preview.

## Features

- **Rich Text Editing**: Full-featured Quill.js editor with toolbar
- **Real-time Preview**: Live display of formatted content below the editor
- **Font Family Support**: Arial, Times New Roman, Helvetica, Georgia, Verdana
- **Text Formatting**: Bold, italic, and color formatting
- **Error Handling**: Graceful fallback to textarea if <PERSON>uil<PERSON> fails to load
- **Responsive Design**: Tailwind CSS styling that adapts to different screen sizes

## Usage

```jsx
import TextEditor from '@/components/TextEditor';

function MyComponent() {
  const [content, setContent] = useState('');

  return (
    <TextEditor
      value={content}
      onChange={setContent}
      placeholder="Start typing..."
      className="w-full"
    />
  );
}
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `value` | string | `''` | The HTML content of the editor |
| `onChange` | function | - | Callback fired when content changes |
| `placeholder` | string | `'Start typing...'` | Placeholder text for empty editor |
| `className` | string | `''` | Additional CSS classes |
| `style` | object | `{}` | Inline styles |
| `disabled` | boolean | `false` | Whether the editor is disabled |

## Toolbar Features

### Text Formatting
- **Bold**: Make text bold
- **Italic**: Make text italic

### Font Family
- Arial
- Times New Roman
- Helvetica
- Georgia
- Verdana

### Color Picker
- Full color palette for text color

## Technical Details

### Dependencies
- `quill`: Rich text editor library
- `quill/dist/quill.snow.css`: Quill Snow theme styles
- `@/styles/quill-fixes.css`: Custom styles for font families and fixes

### Error Handling
If Quill.js fails to initialize, the component automatically falls back to a textarea with the same functionality for basic text input.

### Font Family Implementation
Font families are registered with Quill and styled using CSS classes:
- `.ql-font-Arial`
- `.ql-font-Times-New-Roman`
- `.ql-font-Helvetica`
- `.ql-font-Georgia`
- `.ql-font-Verdana`

### Content Cleaning
The component automatically cleans up empty paragraphs (`<p><br></p>`) that Quill sometimes creates.

## Example

See `/test-text-editor` page for a complete working example with sample content and controls.

## Styling

The component uses Tailwind CSS classes and follows the existing codebase patterns:
- Gray borders and backgrounds
- Hover states
- Focus states with blue accent
- Responsive design
- Loading states with skeleton animations

## Browser Compatibility

Works in all modern browsers that support:
- ES6 modules
- Dynamic imports
- CSS custom properties
- Flexbox
