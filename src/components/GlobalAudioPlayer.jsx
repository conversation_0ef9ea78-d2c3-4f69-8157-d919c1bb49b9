'use client';

import React from 'react';
import { useGlobalAudio } from '@/contexts/GlobalAudioContext';

/**
 * GlobalAudioPlayer Component
 * 
 * A persistent audio player that should be rendered in _app.js
 * to ensure it persists across all page navigation.
 * 
 * Features:
 * - Fixed position at bottom of screen
 * - Minimalist design that doesn't interfere with page content
 * - Can be hidden/shown based on user preference
 * - Responsive design
 */
const GlobalAudioPlayer = ({ 
  position = 'bottom-right', // 'bottom-right', 'bottom-left', 'bottom-center', 'hidden'
  showControls = true,
  className = ""
}) => {
  const { 
    isPlaying, 
    isLoaded, 
    currentTrack, 
    volume, 
    progress, 
    duration, 
    error,
    togglePlayPause, 
    setVolume, 
    setPlaybackTime,
    formattedCurrentTime,
    formattedDuration
  } = useGlobalAudio();

  // Don't render if position is hidden or no track is loaded
  if (position === 'hidden' || !currentTrack) {
    return null;
  }

  const handleVolumeChange = (e) => {
    setVolume(parseFloat(e.target.value));
  };

  const handleProgressClick = (e) => {
    if (!isLoaded || duration === 0) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickRatio = clickX / rect.width;
    const newTime = clickRatio * duration;
    
    setPlaybackTime(newTime);
  };

  // Position classes
  const positionClasses = {
    'bottom-right': 'fixed bottom-4 right-4',
    'bottom-left': 'fixed bottom-4 left-4',
    'bottom-center': 'fixed bottom-4 left-1/2 transform -translate-x-1/2',
  };

  const positionClass = positionClasses[position] || positionClasses['bottom-right'];

  if (!showControls) {
    // Minimal floating indicator
    return (
      <div className={`${positionClass} z-50 ${className}`}>
        <div className="bg-black bg-opacity-80 text-white rounded-full px-4 py-2 shadow-lg backdrop-blur-sm">
          <div className="flex items-center space-x-3">
            <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`} />
            <span className="text-sm font-medium">{currentTrack.title}</span>
            <button
              onClick={togglePlayPause}
              disabled={!isLoaded}
              className="text-white hover:text-blue-300 disabled:text-gray-500 transition-colors"
              title={isPlaying ? 'Pause' : 'Play'}
            >
              {isPlaying ? (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                </svg>
              ) : (
                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M8 5v14l11-7z"/>
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Full controls
  return (
    <div className={`${positionClass} z-50 ${className}`}>
      <div className="bg-white border border-gray-200 rounded-lg shadow-xl p-4 max-w-sm w-80 backdrop-blur-sm bg-opacity-95">
        {/* Error display */}
        {error && (
          <div className="text-red-500 text-xs mb-2 p-2 bg-red-50 rounded">
            {error}
          </div>
        )}

        {/* Track info */}
        <div className="mb-3">
          <h3 className="font-semibold text-sm text-gray-800 truncate">{currentTrack.title}</h3>
          {currentTrack.artist && (
            <p className="text-xs text-gray-500 truncate">{currentTrack.artist}</p>
          )}
        </div>

        {/* Controls */}
        <div className="flex items-center space-x-3 mb-3">
          {/* Play/Pause Button */}
          <button
            onClick={togglePlayPause}
            disabled={!isLoaded}
            className="flex items-center justify-center w-10 h-10 bg-blue-500 text-white rounded-full hover:bg-blue-600 disabled:bg-gray-400 transition-colors"
            title={isPlaying ? 'Pause' : 'Play'}
          >
            {isPlaying ? (
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
              </svg>
            ) : (
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            )}
          </button>

          {/* Progress and Time */}
          <div className="flex-1">
            <div className="flex justify-between text-xs text-gray-600 mb-1">
              <span>{formattedCurrentTime}</span>
              <span>{formattedDuration}</span>
            </div>
            
            {/* Progress bar */}
            <div 
              className="w-full bg-gray-200 rounded-full h-1.5 cursor-pointer hover:bg-gray-300 transition-colors"
              onClick={handleProgressClick}
            >
              <div
                className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                style={{
                  width: duration > 0 ? `${progress * 100}%` : '0%'
                }}
              />
            </div>
          </div>
        </div>

        {/* Volume Control */}
        <div className="flex items-center space-x-2">
          <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02z"/>
          </svg>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={volume}
            onChange={handleVolumeChange}
            className="flex-1 h-1.5 bg-gray-200 rounded-lg appearance-none cursor-pointer accent-blue-500"
          />
          <span className="text-xs text-gray-600 w-8">
            {Math.round(volume * 100)}%
          </span>
        </div>

        {/* Status */}
        <div className="mt-2 text-xs text-gray-500">
          {!isLoaded && 'Loading...'}
          {isLoaded && !isPlaying && 'Ready'}
          {isLoaded && isPlaying && 'Playing'}
        </div>
      </div>
    </div>
  );
};

export default GlobalAudioPlayer;
