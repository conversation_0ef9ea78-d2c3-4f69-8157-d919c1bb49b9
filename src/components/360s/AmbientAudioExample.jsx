'use client';

import { Canvas } from '@react-three/fiber';
import { Suspense } from 'react';
import AmbientAudio from './AmbientAudio';

/**
 * AmbientAudioExample Component
 * 
 * Example implementation showing how to use the AmbientAudio component
 * within a React Three.js Fiber Canvas.
 * 
 * This component demonstrates:
 * - Proper Canvas setup
 * - Suspense boundary for audio loading
 * - Basic scene with ambient audio
 */
function AmbientAudioExample() {
  const handleAudioLoad = () => {
    console.log('Ambient audio loaded and ready to play');
  };

  const handleAudioError = (error) => {
    console.error('Audio loading failed:', error);
  };

  return (
    <div className="w-full h-screen bg-black">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 75 }}
        className="w-full h-full"
      >
        <Suspense fallback={null}>
          {/* Ambient lighting for the scene */}
          <ambientLight intensity={0.5} />
          <pointLight position={[10, 10, 10]} />
          
          {/* Ambient Audio Component */}
          <AmbientAudio
            volume={0.4}
            autoPlay={true}
            loop={true}
            onLoad={handleAudioLoad}
            onError={handleAudioError}
          />
          
          {/* Optional: Add a simple mesh to visualize the scene */}
          <mesh>
            <boxGeometry args={[1, 1, 1]} />
            <meshStandardMaterial color="orange" />
          </mesh>
        </Suspense>
      </Canvas>
      
      {/* UI Controls (optional) */}
      <div className="absolute top-4 left-4 text-white">
        <p className="text-sm">Ambient Audio Example</p>
        <p className="text-xs opacity-70">Audio should start automatically</p>
      </div>
    </div>
  );
}

export default AmbientAudioExample;
