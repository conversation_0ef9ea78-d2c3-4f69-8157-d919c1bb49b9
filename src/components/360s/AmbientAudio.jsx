'use client';

import { useRef, useEffect, useState, useCallback, forwardRef, useImperativeHandle } from 'react';
import { getCachedAudio, preloadAudio } from '@/lib/audio-preloader';

/**
 * Enhanced AmbientAudio Component
 *
 * A React Three.js Fiber component that plays ambient background music
 * with enhanced pause/resume functionality for popup management.
 *
 * Features:
 * - Automatic playback on mount
 * - Continuous looping
 * - Configurable volume
 * - External pause/resume control via ref
 * - Maintains playback position during pause
 * - Prevents audio restarts on re-renders
 * - Error handling for loading failures
 * - Performance optimized with proper cleanup
 *
 * Usage:
 * <Canvas>
 *   <AmbientAudio ref={audioRef} volume={0.4} autoPlay={true} />
 * </Canvas>
 */
const AmbientAudio = forwardRef(function AmbientAudio({
  volume = 0.4,
  autoPlay = true,
  loop = true,
  isPaused = false,
  onLoad = null,
  onError = null,
  onPlayStateChange = null
}, ref) {
  const audioRef = useRef();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isPausedByExternal, setIsPausedByExternal] = useState(isPaused);
  const [error, setError] = useState(null);

  // Audio file path
  const audioUrl = '/assets/Ambient_Music.mp4';

  // Expose control methods via ref
  useImperativeHandle(ref, () => ({
    play: async () => {
      if (audioRef.current && isLoaded) {
        try {
          await audioRef.current.play();
          setIsPlaying(true);
          setIsPausedByExternal(false);
          onPlayStateChange?.(true);
          console.log('Audio play triggered via ref');
        } catch (err) {
          console.error('Ref play failed:', err);
        }
      }
    },
    pause: () => {
      if (audioRef.current && !audioRef.current.paused) {
        audioRef.current.pause();
        setIsPlaying(false);
        setIsPausedByExternal(true);
        onPlayStateChange?.(false);
        console.log('Audio paused via ref');
      }
    },
    resume: async () => {
      if (audioRef.current && audioRef.current.paused && isLoaded) {
        try {
          await audioRef.current.play();
          setIsPlaying(true);
          setIsPausedByExternal(false);
          onPlayStateChange?.(true);
          console.log('Audio resumed via ref');
        } catch (err) {
          console.error('Ref resume failed:', err);
        }
      }
    },
    stop: () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        setIsPlaying(false);
        setIsPausedByExternal(false);
        onPlayStateChange?.(false);
        console.log('Audio stopped via ref');
      }
    },
    get isPlaying() { return isPlaying; },
    get isLoaded() { return isLoaded; },
    get isPaused() { return isPausedByExternal; },
    get currentTime() { return audioRef.current?.currentTime || 0; },
    get duration() { return audioRef.current?.duration || 0; },
    get error() { return error; }
  }), [isLoaded, isPlaying, isPausedByExternal, error, onPlayStateChange]);

  // Load and setup audio - only once, prevent re-creation on re-renders
  useEffect(() => {
    // Don't recreate audio if it already exists and is loaded
    if (audioRef.current && isLoaded) {
      return;
    }

    let isMounted = true;
    let audioElement = null;

    const loadAudio = async () => {
      try {
        console.log('Loading ambient audio from:', audioUrl);

        // First, try to get cached audio
        const cachedAudio = getCachedAudio(audioUrl);

        if (cachedAudio) {
          console.log('🎵 Using preloaded cached audio');
          audioElement = cachedAudio;

          // Configure cached audio element
          audioElement.loop = loop;
          audioElement.volume = volume;

          // Store reference for control methods
          audioRef.current = audioElement;
        } else {
          console.log('📥 Audio not cached, loading with preloader...');

          // Use preloader to load audio with retry logic
          audioElement = await preloadAudio(audioUrl, {
            id: 'ambient-audio',
            timeout: 20000 // 20 second timeout
          });

          // Configure loaded audio element
          audioElement.loop = loop;
          audioElement.volume = volume;

          // Store reference for control methods
          audioRef.current = audioElement;
        }

        // Wait for audio to be ready
        await new Promise((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            reject(new Error('Audio loading timeout'));
          }, 10000);

          const handleCanPlay = () => {
            clearTimeout(timeoutId);
            audioElement.removeEventListener('canplaythrough', handleCanPlay);
            audioElement.removeEventListener('error', handleError);
            audioElement.removeEventListener('loadeddata', handleCanPlay);
            resolve();
          };

          const handleError = (err) => {
            clearTimeout(timeoutId);
            audioElement.removeEventListener('canplaythrough', handleCanPlay);
            audioElement.removeEventListener('error', handleError);
            audioElement.removeEventListener('loadeddata', handleCanPlay);
            reject(err);
          };

          // Listen for multiple ready states
          audioElement.addEventListener('canplaythrough', handleCanPlay);
          audioElement.addEventListener('loadeddata', handleCanPlay);
          audioElement.addEventListener('error', handleError);

          // Start loading
          audioElement.load();
        });

        if (isMounted) {
          setIsLoaded(true);
          setError(null);
          console.log('Ambient audio loaded successfully - ready to play');
          if (onLoad) onLoad();
        }

      } catch (err) {
        console.error('Failed to load ambient audio:', err);
        if (isMounted) {
          setError(err);
          setIsLoaded(false);
          if (onError) onError(err);
        }
      }
    };

    loadAudio();

    return () => {
      isMounted = false;
      // Cleanup HTML5 audio element
      if (audioElement) {
        audioElement.pause();
        audioElement.src = '';
        audioElement.load(); // Reset the element
        audioElement = null;
      }
      audioRef.current = null;
    };
  }, [audioUrl, loop, volume, onLoad, onError]);

  // Handle autoplay and audio control with HTML5 audio
  useEffect(() => {
    if (!audioRef.current || !isLoaded) return;

    const audioElement = audioRef.current;

    // Auto-play if enabled and not externally paused
    if (autoPlay && !isPausedByExternal) {
      const playAudio = async () => {
        try {
          console.log('Attempting to play ambient audio...');
          await audioElement.play();
          setIsPlaying(true);
          onPlayStateChange?.(true);
          console.log('Ambient audio started playing successfully');
        } catch (playError) {
          console.warn('Autoplay prevented by browser policy:', playError.message);

          // Set up a user interaction listener to start audio
          const startOnInteraction = async (event) => {
            console.log('User interaction detected, starting audio...');
            try {
              if (audioElement.paused && !isPausedByExternal) {
                await audioElement.play();
                setIsPlaying(true);
                onPlayStateChange?.(true);
                console.log('Ambient audio started after user interaction');
              }
            } catch (err) {
              console.error('Failed to start audio after interaction:', err);
            }

            // Remove listeners after first successful interaction
            document.removeEventListener('click', startOnInteraction);
            document.removeEventListener('keydown', startOnInteraction);
            document.removeEventListener('touchstart', startOnInteraction);
          };

          // Add interaction listeners
          document.addEventListener('click', startOnInteraction);
          document.addEventListener('keydown', startOnInteraction);
          document.addEventListener('touchstart', startOnInteraction);

          console.log('Added user interaction listeners for audio playback');
        }
      };

      // Small delay to ensure everything is ready
      setTimeout(playAudio, 100);
    }
  }, [isLoaded, autoPlay, isPausedByExternal, onPlayStateChange]);

  // Handle external pause/resume control
  useEffect(() => {
    if (!audioRef.current || !isLoaded) return;

    const audioElement = audioRef.current;

    if (isPaused && !audioElement.paused) {
      // External pause request
      audioElement.pause();
      setIsPlaying(false);
      setIsPausedByExternal(true);
      onPlayStateChange?.(false);
      console.log('Audio paused by external control');
    } else if (!isPaused && audioElement.paused && isPausedByExternal) {
      // External resume request
      const resumeAudio = async () => {
        try {
          await audioElement.play();
          setIsPlaying(true);
          setIsPausedByExternal(false);
          onPlayStateChange?.(true);
          console.log('Audio resumed by external control');
        } catch (err) {
          console.error('Failed to resume audio:', err);
        }
      };
      resumeAudio();
    }
  }, [isPaused, isLoaded, isPausedByExternal, onPlayStateChange]);

  // Update volume when prop changes
  useEffect(() => {
    if (audioRef.current && isLoaded) {
      audioRef.current.volume = volume;
      console.log('Volume updated to:', volume);
    }
  }, [volume, isLoaded]);

  // Don't render anything - this is a non-visual audio component
  return null;
});

export default AmbientAudio;
