'use client'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useContextExperience } from '@/contexts/useContextExperience'
import { useRef, useState } from 'react'
import { HiMenu, HiOutlineMenuAlt3, HiX } from 'react-icons/hi';

export default function _360BtnMenu() {
  const {experienceState,disptachExperience,menuToggle, setMenuToggle} = useContextExperience()
  const menuRef = useRef(null)

  const handleClick = () => {
    // console.log('_360BtnMenu handleClick:')
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.MENU_TOGGLE})
    setMenuToggle(!menuToggle)
  }

  // console.log('_360BtnMenu:',menuToggle)
  return (
    <div className='z-50 text-white flex items-center'>
      <button
        className="z-50 text-4xl mr-2"
        onClick={handleClick}
        aria-label="Toggle menu"
      >
      {menuToggle ? <HiX className={`${menuToggle ? 'invisible' : ''}`} /> : <HiOutlineMenuAlt3 />}
      </button>
    </div>
  )
}
