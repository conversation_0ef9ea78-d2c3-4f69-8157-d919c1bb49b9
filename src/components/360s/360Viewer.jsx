'use client';

import { useState, useEffect, useRef, Suspense, useMemo, useCallback } from 'react';
import { Canvas } from '@react-three/fiber';
import { useRouter } from 'next/navigation';
import PanoramicSphere from './PanoramicSphere';
import LoadingOverlay from './LoadingOverlay';
import FadeTransition from './FadeTransition';
import _360InfoMarkers from './_360InfoMarkers';
import { useContextExperience } from '@/contexts/useContextExperience';
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience';
import AudioControl from '../AudioControl';
// import { sortedArray } from 'three/src/animation/AnimationUtils'; // This import seems unused and might cause issues if not a valid module

// Internal component that uses audio context
function ThreeSixtyViewerInternal({id}) {
  const router = useRouter();
  const [threeSixties, setThreeSixties] = useState([]);
  // Initialize _360Object with default structure to avoid undefined errors
  const [_360Object, set_360Object] = useState({
    cameraPosition: 0,
    _360Rotation: 0,
    markerList: [],
  });
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [resetView, setResetView] = useState(false);
  const [textureCache, setTextureCache] = useState(new Map());
  const [loadingQueue, setLoadingQueue] = useState([]);
  const [error, setError] = useState(null);
  const containerRef = useRef(null);
  const {experienceState,disptachExperience}=useContextExperience()
  // Audio is now handled globally - no local audio management needed

  // Derive currentImage from threeSixties array
  const currentImage = useMemo(() => {
    const image = threeSixties.find(item => item.name === id);
    return image;
  }, [threeSixties, id]);

  // Effect to update _360Object when currentImage changes with proper marker synchronization
  useEffect(() => {
    if (!currentImage) {
      // Clear state when no current image to prevent stale data
      set_360Object({
        _id: '',
        name: '',
        cameraPosition: 0,
        _360Rotation: 0,
        markerList: [],
      });
      return;
    }

    const new_360ObjectState = {
      _id: currentImage._id,
      name: currentImage.name, // Include name for proper state management
      cameraPosition: currentImage.cameraPosition || 0,
      _360Rotation: currentImage._360Rotation || 0,
      markerList: Array.isArray(currentImage.markerList) ? [...currentImage.markerList] : [], // Deep copy to prevent reference issues
    };

    // Always update state when currentImage changes to ensure marker synchronization
    set_360Object(new_360ObjectState);

    let timer;
    setResetView(true);
    timer = setTimeout(() => setResetView(false), 150); // Slightly longer delay for smoother transition

    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [currentImage]);


  // Fetch 360° images from API and refresh when id parameter changes
  useEffect(() => {
    fetchThreeSixties();
    disptachExperience({type:ACTIONS_EXPERIENCE_STATE.RESET});
  }, [id]); // Add id dependency to refresh data when URL parameter changes

  // Add global refresh function for external components to trigger data refresh
  useEffect(() => {
    window.refresh360ViewerData = () => {
      fetchThreeSixties();
    };

    // Cleanup function
    return () => {
      if (window.refresh360ViewerData) {
        delete window.refresh360ViewerData;
      }
    };
  }, []);

  // Enhanced image change handler with proper state clearing
  const handleImageChange = async (index) => {
    if (index === currentImageIndex || isTransitioning) return;

    setIsTransitioning(true);

    // Clear current marker state to prevent stale data during transition
    set_360Object(prev => ({
      ...prev,
      markerList: [] // Clear markers during transition
    }));

    // Wait for fade out
    await new Promise(resolve => setTimeout(resolve, 500));

    setCurrentImageIndex(index);

    // Wait for fade in
    await new Promise(resolve => setTimeout(resolve, 500));

    setIsTransitioning(false);
  };


  // --- End of moved definitions ---


  // Handle keyboard controls
  useEffect(() => {
    const handleKeyDown = (event) => {
      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          if (currentImageIndex > 0) {
            handleImageChange(currentImageIndex - 1);
          }
          break;
        case 'ArrowRight':
          event.preventDefault();
          if (currentImageIndex < threeSixties.length - 1) {
            handleImageChange(currentImageIndex + 1);
          }
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [currentImageIndex, threeSixties.length, handleImageChange]);

  const fetchThreeSixties = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Clear current state to prevent stale data
      set_360Object({
        _id: '',
        name: '',
        cameraPosition: 0,
        _360Rotation: 0,
        markerList: [],
      });

      const response = await fetch('/api/360s?sort=priority&order=asc&limit=50');
      const data = await response.json();

      if (data.success && data.data.length > 0) {
        const sortedData = data.data.sort((b, a) => (a.priority || 0) - (b.priority || 0));
        setThreeSixties(sortedData);

        setCurrentImageIndex(0);
        initializeTextureLoading(sortedData);
      } else {
        setError('No 360° images found');
      }
    } catch (err) {
      console.error('Error fetching 360° images:', err);
      setError('Failed to load 360° images');
    } finally {
      setIsLoading(false);
    }
  };

  const initializeTextureLoading = (images) => {
    const sortedImages = [...images].sort((b, a) => (a.priority || 0) - (b.priority || 0));

    setLoadingQueue(sortedImages.map((img, index) => ({
      ...img,
      originalIndex: images.findIndex(item => item._id === img._id),
      priority: img.priority || 0,
      loadOrder: index,
      status: 'pending'
    })));
  };

  const handleBack = () => {
    router.push('/');
  };

  // Enhanced URL resolution for production compatibility
  const resolveImageUrl = useCallback((url) => {
    if (!url) return null;

    // If it's already a full URL (Firebase, CDN), return as-is
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }

    // For local files, use our serving API in production
    if (process.env.NODE_ENV === 'production') {
      // Extract filename from path
      const filename = url.split('/').pop();
      return `/api/360s/serve/${filename}`;
    }

    // In development, use direct path
    return url.startsWith('/') ? url : `/${url}`;
  }, []);

  // Memoize the props object specifically for PanoramicSphere
  const panoSphereProps = useMemo(() => {
    const resolvedUrl = currentImage?.url ? resolveImageUrl(currentImage.url) : null;

    return {
      currentImage: currentImage,
      imageUrl: resolvedUrl,
      imageId: currentImage?._id,
      textureCache: textureCache,
      setTextureCache: setTextureCache,
      loadingQueue: loadingQueue,
      setLoadingQueue: setLoadingQueue,
      _360Object: _360Object, // Pass the actual _360Object state
      set_360Object: set_360Object,
      onTextureLoad: () => { /* Texture loaded successfully */ },
      resetView: resetView,
    };
  }, [
    currentImage,
    textureCache,
    setTextureCache,
    loadingQueue,
    setLoadingQueue,
    _360Object, // Dependency for _360Object state
    resetView,
    resolveImageUrl,
  ]);

  // Memoize the props object specifically for _360InfoMarkers with proper dependencies
  const infoMarkersProps = useMemo(() => {
    return {
      markerList: _360Object?.markerList || [],
      disptachExperience: disptachExperience,
      experienceState: experienceState,
      currentImageId: _360Object?._id, // Add current image ID for proper re-rendering
    };
  }, [_360Object?.markerList, _360Object?._id, disptachExperience, experienceState]);


  if (isLoading) {
    return <LoadingOverlay message="Loading 360° images..." />;
  }

  if (error) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">Error Loading 360° Viewer</h2>
          <p className="text-gray-300 mb-6">{error}</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Home
          </button>
        </div>
      </div>
    );
  }

  if (threeSixties.length === 0) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <h2 className="text-2xl font-bold mb-4">No 360° Images Available</h2>
          <p className="text-gray-300 mb-6">Upload some 360° images to get started.</p>
          <button
            onClick={handleBack}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors"
          >
            Back to Manager
          </button>
        </div>
      </div>
    );
  }

  // console.log(_360Object)

  return (
    <div 
      ref={containerRef}
      className="relative w-full h-screen bg-black overflow-hidden"
    >
      {/* 3D Canvas */}
      <Canvas
        camera={{ position: [0, 0, 0.1], fov: 100 }}
        className="w-full h-full"
        gl={{
          antialias: true,
          alpha: false,
          preserveDrawingBuffer: false
        }}
      >
        {/* Audio is now handled globally via GlobalAudioPlayer in layout.js */}
        <Suspense fallback={null}>
          {currentImage && ( // Only render PanoramicSphere if currentImage is available
            <PanoramicSphere {...panoSphereProps} />
          )}
          {/* Render _360InfoMarkers only if we have a valid 360Object and marker list */}
          {_360Object?._id && _360Object?.markerList && Array.isArray(_360Object.markerList) && (
            <_360InfoMarkers {...infoMarkersProps} />
          )}
        </Suspense>
      </Canvas>

      {/* Fade Transition Overlay */}
      <FadeTransition isTransitioning={isTransitioning} />

      {/* Loading Overlay for texture loading */}
      {isLoading && (
        <LoadingOverlay message="Loading textures..." />
      )}

      {/* Optional Audio Control - can be enabled if needed */}
      {/*
      <div className="absolute top-4 right-4 z-10">
        <AudioControl variant="icon-only" size="medium" />
      </div>
      */}
    </div>
  );
}

// Main export component with AudioContextProvider wrapper
export default function ThreeSixtyViewer({ id }) {
  return (
    <AudioContextProvider>
      <ThreeSixtyViewerInternal id={id} />
    </AudioContextProvider>
  );
}
