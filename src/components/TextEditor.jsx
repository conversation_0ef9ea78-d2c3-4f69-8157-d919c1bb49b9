'use client';

import React, { useState, useEffect, useRef } from 'react';

const TextEditor = ({
  value = '',
  onChange,
  placeholder = 'Start typing...',
  className = '',
  style = {},
  disabled = false,
  minHeight = '200px'
}) => {
  const [content, setContent] = useState(value);
  const [selectedText, setSelectedText] = useState('');
  const [currentFont, setCurrentFont] = useState('Arial');
  const [currentColor, setCurrentColor] = useState('#000000');
  const [currentFontSize, setCurrentFontSize] = useState('40px');
  const editorRef = useRef(null);
  const isUpdatingRef = useRef(false);

  // Initialize content when component mounts
  useEffect(() => {
    if (editorRef.current && !editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value || '';
      setContent(value || '');
    }
  }, []);

  // Update content when value prop changes (but not during user editing)
  useEffect(() => {
    if (value !== content && !isUpdatingRef.current && editorRef.current) {
      setContent(value);
      // Only update innerHTML if it's actually different to avoid cursor issues
      if (editorRef.current.innerHTML !== value) {
        const selection = window.getSelection();
        const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
        const cursorPosition = range ? range.startOffset : 0;

        editorRef.current.innerHTML = value;

        // Try to restore cursor position
        if (range && editorRef.current.firstChild) {
          try {
            const newRange = document.createRange();
            const textNode = editorRef.current.firstChild;
            const maxOffset = textNode.textContent ? Math.min(cursorPosition, textNode.textContent.length) : 0;
            newRange.setStart(textNode, maxOffset);
            newRange.setEnd(textNode, maxOffset);
            selection.removeAllRanges();
            selection.addRange(newRange);
          } catch (e) {
            // Ignore cursor restoration errors
          }
        }
      }
    }
  }, [value, content]);

  const handleContentChange = (e) => {
    if (isUpdatingRef.current) return;

    const newContent = e.target.innerHTML;
    setContent(newContent);
    if (onChange) {
      onChange(newContent);
    }
  };

  const applyFormat = (command, value = null) => {
    // Save current selection
    const selection = window.getSelection();
    const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;

    if (range) {
      // Apply formatting to selected text
      document.execCommand(command, false, value);
    } else {
      // If no selection, focus and apply formatting for new text
      editorRef.current?.focus();
      document.execCommand(command, false, value);
    }

    // Update content after formatting
    isUpdatingRef.current = true;
    setTimeout(() => {
      const newContent = editorRef.current?.innerHTML || '';
      setContent(newContent);
      if (onChange) {
        onChange(newContent);
      }
      isUpdatingRef.current = false;
    }, 10);
  };

  const handleFontChange = (e) => {
    const font = e.target.value;
    setCurrentFont(font);
    applyFormat('fontName', font);
  };

  const handleColorChange = (e) => {
    const color = e.target.value;
    setCurrentColor(color);
    applyFormat('foreColor', color);
  };

  const handleFontSizeChange = (e) => {
    const fontSize = e.target.value;
    setCurrentFontSize(fontSize);

    // Use CSS styling approach for better control
    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      if (!range.collapsed) {
        // Apply to selected text
        const span = document.createElement('span');
        span.style.fontSize = fontSize;
        try {
          range.surroundContents(span);
        } catch (e) {
          // If surroundContents fails, extract and wrap content
          const contents = range.extractContents();
          span.appendChild(contents);
          range.insertNode(span);
        }
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }

    // Update content
    isUpdatingRef.current = true;
    setTimeout(() => {
      const newContent = editorRef.current?.innerHTML || '';
      setContent(newContent);
      if (onChange) {
        onChange(newContent);
      }
      isUpdatingRef.current = false;
    }, 10);
  };

  const handleSelectionChange = () => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      setSelectedText(selection.toString());

      // Try to detect current formatting of selected text
      const range = selection.getRangeAt(0);
      const parentElement = range.commonAncestorContainer.nodeType === Node.TEXT_NODE
        ? range.commonAncestorContainer.parentElement
        : range.commonAncestorContainer;

      if (parentElement) {
        const computedStyle = window.getComputedStyle(parentElement);
        const fontSize = computedStyle.fontSize;
        if (fontSize) {
          setCurrentFontSize(fontSize);
        }
      }
    } else {
      setSelectedText('');
    }
  };

  return (
    <div className={`text-editor-container ${className}`} style={style}>
      {/* Editor Section */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-800 mb-3">
          Rich Text Editor
        </h3>

        <div className="border border-gray-300 rounded-md overflow-hidden">
          {/* Custom Toolbar */}
          <div className="bg-gray-50 border-b border-gray-300 p-2 flex items-center gap-2 flex-wrap">
            {/* Bold Button */}
            <button
              type="button"
              onClick={() => applyFormat('bold')}
              className="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-100 font-bold"
              disabled={disabled}
              title="Bold (Ctrl+B)"
            >
              B
            </button>

            {/* Italic Button */}
            <button
              type="button"
              onClick={() => applyFormat('italic')}
              className="px-3 py-1 bg-white border border-gray-300 rounded hover:bg-gray-100 italic"
              disabled={disabled}
              title="Italic (Ctrl+I)"
            >
              I
            </button>

            {/* Font Family Selector */}
            <select
              value={currentFont}
              onChange={handleFontChange}
              disabled={disabled}
              className="px-2 py-1 bg-white border border-gray-300 rounded text-sm"
              title="Font Family"
            >
              <option value="Arial">Arial</option>
              <option value="Times New Roman">Times New Roman</option>
              <option value="Helvetica">Helvetica</option>
              <option value="Georgia">Georgia</option>
              <option value="Verdana">Verdana</option>
            </select>

            {/* Font Size Selector */}
            <select
              value={currentFontSize}
              onChange={handleFontSizeChange}
              disabled={disabled}
              className="px-2 py-1 bg-white border border-gray-300 rounded text-sm"
              title="Font Size"
            >
              <option value="60px">Large (60px)</option>
              <option value="40px">Medium (40px)</option>
              <option value="28px">Small (28px)</option>
              <option value="20px">Extra Small (20px)</option>
            </select>

            {/* Color Picker */}
            <div className="flex items-center gap-1">
              <label htmlFor="color-picker" className="text-sm text-gray-600">Color:</label>
              <input
                id="color-picker"
                type="color"
                value={currentColor}
                onChange={handleColorChange}
                disabled={disabled}
                className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
                title="Text Color"
              />
            </div>
          </div>

          {/* Editor Content */}
          <div
            ref={editorRef}
            contentEditable={!disabled}
            onInput={handleContentChange}
            onMouseUp={handleSelectionChange}
            onKeyUp={handleSelectionChange}
            onFocus={handleSelectionChange}
            onBlur={() => setSelectedText('')}
            className="p-4 bg-white focus:outline-none"
            style={{ minHeight }}
            data-placeholder={placeholder}
            suppressContentEditableWarning={true}
          />
        </div>
      </div>

      {/* Preview Section */}
      <div className="preview-section">
        <h3 className="text-lg font-medium text-gray-800 mb-3">
          Formatted Preview
        </h3>
        <div className="border border-gray-200 rounded-md p-4 min-h-[150px] bg-gray-50">
          {content ? (
            <div
              className="formatted-content"
              dangerouslySetInnerHTML={{ __html: content }}
            />
          ) : (
            <p className="text-gray-500 italic">
              Your formatted text will appear here...
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default TextEditor;
