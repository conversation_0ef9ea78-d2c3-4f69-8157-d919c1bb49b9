'use client';

import { useEffect, useState } from 'react';
import { preloadEssentialAudio, getAudioCacheStats, AUDIO_ASSETS } from '@/lib/audio-preloader';

/**
 * AudioPreloader Component
 * 
 * Invisible component that handles early audio preloading
 * and provides optional loading feedback to the user.
 */
export default function AudioPreloader({ 
  showProgress = false, 
  onPreloadComplete = null,
  onPreloadError = null 
}) {
  const [preloadStatus, setPreloadStatus] = useState('idle');
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);

  useEffect(() => {
    let isMounted = true;

    const startPreloading = async () => {
      if (!isMounted) return;

      try {
        setPreloadStatus('loading');
        setProgress(0);
        setError(null);

        console.log('🎵 Starting early audio preloading...');

        // Get total number of audio files to preload
        const audioFiles = Object.entries(AUDIO_ASSETS);
        const totalFiles = audioFiles.length;
        let completedFiles = 0;

        // Preload each audio file with progress tracking
        const preloadPromises = audioFiles.map(async ([key, src]) => {
          try {
            console.log(`📥 Preloading ${key}:`, src);
            
            // Import the preloadAudio function dynamically to avoid circular imports
            const { preloadAudio } = await import('@/lib/audio-preloader');
            await preloadAudio(src, { 
              id: key,
              timeout: 25000 // 25 second timeout for each file
            });
            
            completedFiles++;
            const newProgress = Math.round((completedFiles / totalFiles) * 100);
            
            if (isMounted) {
              setProgress(newProgress);
              console.log(`✅ Preloaded ${key} (${newProgress}% complete)`);
            }
            
          } catch (error) {
            console.error(`❌ Failed to preload ${key}:`, error.message);
            // Don't throw - continue with other files
            completedFiles++;
            if (isMounted) {
              setProgress(Math.round((completedFiles / totalFiles) * 100));
            }
          }
        });

        // Wait for all preloads to complete
        await Promise.allSettled(preloadPromises);

        if (isMounted) {
          setPreloadStatus('completed');
          setProgress(100);
          
          // Get final cache stats
          const stats = getAudioCacheStats();
          console.log('📊 Audio preloading completed. Cache stats:', stats);
          
          // Call completion callback
          if (onPreloadComplete) {
            onPreloadComplete(stats);
          }
        }

      } catch (error) {
        console.error('❌ Audio preloading failed:', error);
        
        if (isMounted) {
          setPreloadStatus('error');
          setError(error.message);
          
          if (onPreloadError) {
            onPreloadError(error);
          }
        }
      }
    };

    // Start preloading immediately but allow React to finish initial render
    const timeoutId = setTimeout(startPreloading, 100);

    return () => {
      isMounted = false;
      clearTimeout(timeoutId);
    };
  }, [onPreloadComplete, onPreloadError]);

  // Don't render anything by default
  if (!showProgress) {
    return null;
  }

  // Optional progress display
  return (
    <div className="audio-preloader fixed bottom-4 left-4 z-50 bg-black/80 text-white p-2 rounded text-xs">
      {preloadStatus === 'loading' && (
        <div className="flex items-center space-x-2">
          <div className="animate-spin w-3 h-3 border border-white border-t-transparent rounded-full"></div>
          <span>Loading audio... {progress}%</span>
        </div>
      )}
      
      {preloadStatus === 'completed' && (
        <div className="flex items-center space-x-2 text-green-400">
          <span>✅</span>
          <span>Audio ready</span>
        </div>
      )}
      
      {preloadStatus === 'error' && (
        <div className="flex items-center space-x-2 text-red-400">
          <span>❌</span>
          <span>Audio load failed</span>
        </div>
      )}
    </div>
  );
}

/**
 * Hook to get audio preload status
 */
export function useAudioPreloadStatus() {
  const [status, setStatus] = useState('idle');
  const [stats, setStats] = useState(null);

  useEffect(() => {
    const checkStatus = () => {
      const cacheStats = getAudioCacheStats();
      setStats(cacheStats);
      
      if (cacheStats.totalCached > 0) {
        setStatus('loaded');
      } else {
        setStatus('idle');
      }
    };

    // Check immediately
    checkStatus();

    // Check periodically
    const interval = setInterval(checkStatus, 2000);

    return () => clearInterval(interval);
  }, []);

  return { status, stats };
}

/**
 * Audio Preload Status Indicator Component
 */
export function AudioPreloadIndicator({ className = '' }) {
  const { status, stats } = useAudioPreloadStatus();

  if (status === 'idle') return null;

  return (
    <div className={`audio-status-indicator ${className}`}>
      {status === 'loaded' && (
        <div className="flex items-center space-x-1 text-green-500 text-xs">
          <span>🎵</span>
          <span>{stats?.totalCached || 0} audio files cached</span>
        </div>
      )}
    </div>
  );
}
