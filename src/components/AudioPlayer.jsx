'use client';

import { useRef, useEffect, useState } from 'react';

/**
 * AudioPlayer Component
 * 
 * A simple audio playback component that plays Ambient_Music.mp4 from the assets folder.
 * Features:
 * - Automatic playback on mount
 * - Continuous looping
 * - Volume control
 * - Play/pause controls
 * - Error handling
 */
export default function AudioPlayer({ 
  volume = 0.4, 
  autoPlay = true, 
  loop = true,
  showControls = false,
  className = ""
}) {
  const audioRef = useRef(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(null);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  // Audio file path from public/assets folder
  const audioUrl = '/assets/Ambient_Music.mp4';

  // Initialize audio element
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    // Set audio properties
    audio.src = audioUrl;
    audio.volume = volume;
    audio.loop = loop;

    // Event handlers
    const handleLoadedData = () => {
      setIsLoaded(true);
      setDuration(audio.duration);
      setError(null);
      console.log('Audio loaded successfully');
    };

    const handlePlay = () => {
      setIsPlaying(true);
      console.log('Audio started playing');
    };

    const handlePause = () => {
      setIsPlaying(false);
      console.log('Audio paused');
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handleError = (e) => {
      const errorMsg = `Audio loading failed: ${e.target.error?.message || 'Unknown error'}`;
      setError(errorMsg);
      setIsLoaded(false);
      console.error(errorMsg);
    };

    const handleEnded = () => {
      if (!loop) {
        setIsPlaying(false);
        console.log('Audio playback ended');
      }
    };

    // Add event listeners
    audio.addEventListener('loadeddata', handleLoadedData);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('error', handleError);
    audio.addEventListener('ended', handleEnded);

    // Load the audio
    audio.load();

    // Cleanup
    return () => {
      audio.removeEventListener('loadeddata', handleLoadedData);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('error', handleError);
      audio.removeEventListener('ended', handleEnded);
    };
  }, [audioUrl, volume, loop]);

  // Handle autoplay
  useEffect(() => {
    if (isLoaded && autoPlay && !isPlaying) {
      playAudio();
    }
  }, [isLoaded, autoPlay]);

  // Update volume when prop changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  // Play audio function
  const playAudio = async () => {
    if (!audioRef.current || !isLoaded) return;

    try {
      await audioRef.current.play();
    } catch (err) {
      console.warn('Autoplay prevented by browser policy:', err.message);
      
      // Set up user interaction listener for autoplay
      const handleUserInteraction = async () => {
        try {
          if (audioRef.current && audioRef.current.paused) {
            await audioRef.current.play();
            console.log('Audio started after user interaction');
          }
        } catch (playErr) {
          console.error('Failed to play audio after interaction:', playErr);
        }
        
        // Remove listeners after first interaction
        document.removeEventListener('click', handleUserInteraction);
        document.removeEventListener('keydown', handleUserInteraction);
        document.removeEventListener('touchstart', handleUserInteraction);
      };

      // Add interaction listeners
      document.addEventListener('click', handleUserInteraction);
      document.addEventListener('keydown', handleUserInteraction);
      document.addEventListener('touchstart', handleUserInteraction);
    }
  };

  // Pause audio function
  const pauseAudio = () => {
    if (audioRef.current && !audioRef.current.paused) {
      audioRef.current.pause();
    }
  };

  // Toggle play/pause
  const togglePlayPause = () => {
    if (isPlaying) {
      pauseAudio();
    } else {
      playAudio();
    }
  };

  // Format time for display
  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  if (error) {
    return (
      <div className={`text-red-500 text-sm ${className}`}>
        Audio Error: {error}
      </div>
    );
  }

  return (
    <div className={className}>
      {/* Hidden audio element */}
      <audio ref={audioRef} preload="auto" />
      
      {/* Optional controls */}
      {showControls && (
        <div className="flex items-center space-x-4 p-4 bg-gray-100 rounded-lg">
          <button
            onClick={togglePlayPause}
            disabled={!isLoaded}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:bg-gray-400"
          >
            {isPlaying ? 'Pause' : 'Play'}
          </button>
          
          <div className="flex-1">
            <div className="text-sm text-gray-600">
              {isLoaded ? (
                `${formatTime(currentTime)} / ${formatTime(duration)}`
              ) : (
                'Loading...'
              )}
            </div>
            
            {/* Progress bar */}
            <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
              <div
                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                style={{
                  width: duration > 0 ? `${(currentTime / duration) * 100}%` : '0%'
                }}
              />
            </div>
          </div>
          
          <div className="text-sm text-gray-600">
            Volume: {Math.round(volume * 100)}%
          </div>
        </div>
      )}
      
      {/* Status indicator */}
      {!showControls && (
        <div className="text-xs text-gray-500">
          {isLoaded ? (isPlaying ? '🎵 Playing' : '⏸️ Paused') : '⏳ Loading...'}
        </div>
      )}
    </div>
  );
}
