# TextEditor Font Functionality Troubleshooting

## How to Verify Font Features Are Working

### 1. Font Family Dropdown
**Location**: Second button in the toolbar (should show "Sans Serif" by default)

**How to test**:
1. Open `/test-fonts` page
2. Click the "Load Font Samples" button
3. Look for the font dropdown in the toolbar (between Bold/Italic and Color picker)
4. Click the dropdown - you should see:
   - Sans Serif (default)
   - Arial
   - Times New Roman
   - Helvetica
   - Georgia
   - Verdana

**Expected behavior**:
- Each font name should appear in its respective font family
- Selecting a font should apply it to selected text or new text
- The preview section should show the font changes immediately

### 2. Font Preview in Editor
**How to test**:
1. Type some text in the editor
2. Select the text
3. Choose different fonts from the dropdown
4. The text should visually change fonts in both the editor and preview

### 3. Font Persistence
**How to test**:
1. Apply different fonts to different text sections
2. Save/reload the content
3. Fonts should be preserved in the HTML output
4. Check the "Raw HTML Output" section for font classes like `ql-font-Arial`

## Common Issues and Solutions

### Issue 1: Font Dropdown Not Visible
**Symptoms**: Can't see font dropdown in toolbar
**Solution**: 
- Check that Quill.js loaded properly (no console errors)
- Verify `quill/dist/quill.snow.css` is imported
- Check browser developer tools for CSS conflicts

### Issue 2: Font Names Not Showing in Dropdown
**Symptoms**: Dropdown shows but no font names
**Solution**:
- Check `src/styles/quill-fixes.css` is loaded
- Look for CSS rules with `ql-picker-label[data-value="Arial"]::before`
- Verify font registration in TextEditor.jsx

### Issue 3: Fonts Not Applying to Text
**Symptoms**: Can select fonts but text doesn't change
**Solution**:
- Check for CSS class conflicts
- Verify `.ql-font-Arial` etc. classes exist in CSS
- Check browser developer tools for applied styles

### Issue 4: Fonts Not Preserved in Preview
**Symptoms**: Editor shows fonts but preview doesn't
**Solution**:
- Check `.formatted-content` CSS rules
- Verify `dangerouslySetInnerHTML` is rendering properly
- Look for CSS specificity issues

## Technical Implementation Details

### Font Registration
```javascript
const Font = Quill.import('formats/font');
Font.whitelist = ['Arial', 'Times-New-Roman', 'Helvetica', 'Georgia', 'Verdana'];
Quill.register(Font, true);
```

### CSS Classes Generated
- `.ql-font-Arial` - Arial font
- `.ql-font-Times-New-Roman` - Times New Roman font
- `.ql-font-Helvetica` - Helvetica font
- `.ql-font-Georgia` - Georgia font
- `.ql-font-Verdana` - Verdana font

### HTML Output Example
```html
<p><span class="ql-font-Arial">This is Arial text</span></p>
<p><span class="ql-font-Georgia">This is Georgia text</span></p>
```

## Testing Pages

1. **`/test-fonts`** - Comprehensive font testing with samples
2. **`/test-text-editor`** - Basic editor functionality
3. **`/text-editor-form-example`** - Form integration example

## Browser Developer Tools Debugging

### Check Font Classes
1. Open browser developer tools
2. Inspect text in the editor
3. Look for `class="ql-font-Arial"` etc. on span elements
4. Verify CSS rules are applied

### Check CSS Loading
1. Go to Network tab
2. Reload page
3. Verify `quill.snow.css` and `quill-fixes.css` load successfully
4. Check for 404 errors

### Check Console Errors
1. Open Console tab
2. Look for Quill-related errors
3. Check for font registration errors
4. Verify no CSS parsing errors

## Manual Testing Checklist

- [ ] Font dropdown is visible in toolbar
- [ ] Font dropdown shows all 5 font options
- [ ] Font names appear in their respective fonts
- [ ] Selecting fonts applies to selected text
- [ ] New text uses selected font
- [ ] Fonts appear correctly in preview section
- [ ] HTML output contains correct font classes
- [ ] Fonts persist after page reload
- [ ] Multiple fonts can be used in same document
- [ ] Font formatting combines with bold/italic/color

## If Fonts Still Don't Work

1. **Check Quill Version**: Ensure react-quill@2.0.0 is installed
2. **Clear Browser Cache**: Hard refresh (Ctrl+F5 or Cmd+Shift+R)
3. **Check CSS Specificity**: Other CSS might be overriding font styles
4. **Verify Imports**: Ensure all CSS files are properly imported
5. **Test in Different Browser**: Rule out browser-specific issues

## Alternative Font Implementation

If the current implementation doesn't work, you can try this alternative approach:

```javascript
// In TextEditor.jsx, replace font registration with:
const fontAttributor = Quill.import('attributors/class/font');
fontAttributor.whitelist = ['Arial', 'Times-New-Roman', 'Helvetica', 'Georgia', 'Verdana'];
Quill.register(fontAttributor, true);
```
