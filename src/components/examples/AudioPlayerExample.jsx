'use client';

import { useState } from 'react';
import AudioPlayer from '../AudioPlayer';

/**
 * AudioPlayerExample Component
 * 
 * Demonstrates different ways to use the AudioPlayer component
 */
export default function AudioPlayerExample() {
  const [volume, setVolume] = useState(0.4);
  const [showControls, setShowControls] = useState(true);
  const [autoPlay, setAutoPlay] = useState(true);
  const [loop, setLoop] = useState(true);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <h1 className="text-3xl font-bold text-center mb-8">
        AudioPlayer Component Examples
      </h1>

      {/* Configuration Panel */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Configuration</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              Volume: {Math.round(volume * 100)}%
            </label>
            <input
              type="range"
              min="0"
              max="1"
              step="0.1"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
              className="w-full"
            />
          </div>
          
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={showControls}
                onChange={(e) => setShowControls(e.target.checked)}
                className="mr-2"
              />
              Show Controls
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={autoPlay}
                onChange={(e) => setAutoPlay(e.target.checked)}
                className="mr-2"
              />
              Auto Play
            </label>
            
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={loop}
                onChange={(e) => setLoop(e.target.checked)}
                className="mr-2"
              />
              Loop
            </label>
          </div>
        </div>
      </div>

      {/* Example 1: Full Controls */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">Example 1: With Controls</h2>
        <AudioPlayer
          volume={volume}
          autoPlay={autoPlay}
          loop={loop}
          showControls={showControls}
          className="w-full"
        />
      </div>

      {/* Example 2: Hidden Player */}
      <div className="bg-white p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">Example 2: Background Audio (No Controls)</h2>
        <p className="text-gray-600 mb-4">
          This player runs in the background with minimal UI. Perfect for ambient audio.
        </p>
        <AudioPlayer
          volume={volume * 0.5} // Quieter for background
          autoPlay={autoPlay}
          loop={loop}
          showControls={false}
          className="inline-block"
        />
      </div>

      {/* Example 3: Custom Styled */}
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-lg border">
        <h2 className="text-xl font-semibold mb-4">Example 3: Custom Styled</h2>
        <div className="bg-white rounded-lg p-4">
          <AudioPlayer
            volume={volume}
            autoPlay={autoPlay}
            loop={loop}
            showControls={true}
            className="w-full"
          />
        </div>
      </div>

      {/* Usage Instructions */}
      <div className="bg-blue-50 p-6 rounded-lg">
        <h2 className="text-xl font-semibold mb-4">Usage Instructions</h2>
        <div className="space-y-4 text-sm">
          <div>
            <h3 className="font-medium">Basic Usage:</h3>
            <pre className="bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
{`import AudioPlayer from './components/AudioPlayer';

<AudioPlayer 
  volume={0.4}
  autoPlay={true}
  loop={true}
  showControls={false}
/>`}
            </pre>
          </div>
          
          <div>
            <h3 className="font-medium">Props:</h3>
            <ul className="list-disc list-inside space-y-1 mt-1">
              <li><code>volume</code> - Audio volume (0.0 to 1.0, default: 0.4)</li>
              <li><code>autoPlay</code> - Start playing automatically (default: true)</li>
              <li><code>loop</code> - Loop the audio (default: true)</li>
              <li><code>showControls</code> - Show play/pause controls (default: false)</li>
              <li><code>className</code> - Additional CSS classes</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium">Features:</h3>
            <ul className="list-disc list-inside space-y-1 mt-1">
              <li>Plays Ambient_Music.mp4 from /assets/ folder</li>
              <li>Handles browser autoplay restrictions</li>
              <li>Error handling and loading states</li>
              <li>Progress tracking and time display</li>
              <li>Responsive design with Tailwind CSS</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
