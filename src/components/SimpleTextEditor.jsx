'use client';

import React, { useEffect, useRef, useState } from 'react';

const SimpleTextEditor = () => {
  const editorRef = useRef(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [quillLoaded, setQuillLoaded] = useState(false);

  useEffect(() => {
    const loadQuill = async () => {
      try {
        console.log('Attempting to load Quill...');
        
        // First, try to load the CSS
        const cssLink = document.createElement('link');
        cssLink.rel = 'stylesheet';
        cssLink.href = 'https://cdn.quilljs.com/1.3.6/quill.snow.css';
        document.head.appendChild(cssLink);
        
        // Wait for CSS to load
        await new Promise((resolve) => {
          cssLink.onload = resolve;
          setTimeout(resolve, 1000); // fallback timeout
        });
        
        console.log('CSS loaded, now loading Quill JS...');
        
        // Try to import Quill
        const Quill = (await import('quill')).default;
        console.log('Quill imported successfully:', Quill);
        
        if (!editorRef.current) {
          console.log('Editor ref not available');
          return;
        }
        
        console.log('Creating Quill instance...');
        const quill = new Quill(editorRef.current, {
          theme: 'snow',
          placeholder: 'Type something...',
          modules: {
            toolbar: [
              ['bold', 'italic'],
              [{ 'color': [] }]
            ]
          }
        });
        
        console.log('Quill instance created successfully:', quill);
        setQuillLoaded(true);
        setIsLoading(false);
        
      } catch (err) {
        console.error('Error loading Quill:', err);
        setError(err.message);
        setIsLoading(false);
      }
    };
    
    loadQuill();
  }, []);

  if (error) {
    return (
      <div className="p-4 border border-red-300 rounded bg-red-50">
        <h3 className="text-red-600 font-medium">Error loading editor</h3>
        <p className="text-red-600 text-sm mt-1">{error}</p>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="p-4 border border-gray-300 rounded bg-gray-50">
        <h3 className="text-gray-600 font-medium">Loading editor...</h3>
        <div className="mt-2 h-4 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className="border border-gray-300 rounded">
      <div ref={editorRef} style={{ minHeight: '200px' }} />
      {quillLoaded && (
        <div className="p-2 bg-green-50 border-t border-green-200">
          <p className="text-green-600 text-sm">✓ Quill editor loaded successfully!</p>
        </div>
      )}
    </div>
  );
};

export default SimpleTextEditor;
