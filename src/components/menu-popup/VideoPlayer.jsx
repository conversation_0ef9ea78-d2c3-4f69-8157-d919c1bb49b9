import React, { useState, useRef, useEffect, useCallback } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'

export default function VideoPlayer({ data, setShowVideoPlayer }) {
  const { experienceState, disptachExperience } = useContextExperience()
  const videoRef = useRef(null)
  const progressRef = useRef(null)
  const volumeRef = useRef(null)
  const controlsTimeoutRef = useRef(null)

  // Video states
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [volume, setVolume] = useState(1)
  const [isMuted, setIsMuted] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [showControls, setShowControls] = useState(true)
  const [isBuffering, setIsBuffering] = useState(false)

  // Format time helper
  const formatTime = useCallback((time) => {
    if (isNaN(time)) return '0:00'
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }, [])

  // Toggle play/pause
  const togglePlayPause = useCallback(() => {
    if (!videoRef.current) return

    if (isPlaying) {
      videoRef.current.pause()
    } else {
      videoRef.current.play()
    }
  }, [isPlaying])

  // Handle volume change
  const handleVolumeChange = useCallback((e) => {
    const newVolume = parseFloat(e.target.value)
    setVolume(newVolume)
    if (videoRef.current) {
      videoRef.current.volume = newVolume
      setIsMuted(newVolume === 0)
    }
  }, [])

  // Toggle mute
  const toggleMute = useCallback(() => {
    if (!videoRef.current) return

    if (isMuted) {
      videoRef.current.volume = volume
      setIsMuted(false)
    } else {
      videoRef.current.volume = 0
      setIsMuted(true)
    }
  }, [isMuted, volume])

  // Seek to specific time
  const handleSeek = useCallback((e) => {
    if (!videoRef.current || !progressRef.current) return

    const rect = progressRef.current.getBoundingClientRect()
    const clickX = e.clientX - rect.left
    const newTime = (clickX / rect.width) * duration

    videoRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }, [duration])

  // Skip forward/backward
  const skipTime = useCallback((seconds) => {
    if (!videoRef.current) return

    const newTime = Math.max(0, Math.min(duration, currentTime + seconds))
    videoRef.current.currentTime = newTime
    setCurrentTime(newTime)
  }, [currentTime, duration])

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      videoRef.current?.requestFullscreen?.()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen?.()
      setIsFullscreen(false)
    }
  }, [])

  // Close video player
  const handleVideoPlayerClose = useCallback(() => {
    if (videoRef.current) {
      videoRef.current.pause()
    }
    setShowVideoPlayer(false)
  }, [setShowVideoPlayer])

  // Keyboard controls
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (e.target.tagName === 'INPUT') return

      switch (e.code) {
        case 'Space':
          e.preventDefault()
          togglePlayPause()
          break
        case 'ArrowLeft':
          e.preventDefault()
          skipTime(-10)
          break
        case 'ArrowRight':
          e.preventDefault()
          skipTime(10)
          break
        case 'ArrowUp':
          e.preventDefault()
          setVolume(prev => Math.min(1, prev + 0.1))
          break
        case 'ArrowDown':
          e.preventDefault()
          setVolume(prev => Math.max(0, prev - 0.1))
          break
        case 'KeyM':
          e.preventDefault()
          toggleMute()
          break
        case 'KeyF':
          e.preventDefault()
          toggleFullscreen()
          break
        default:
          break
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [togglePlayPause, skipTime, toggleMute, toggleFullscreen])

  // Auto-hide controls
  useEffect(() => {
    const resetControlsTimeout = () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }

      setShowControls(true)

      if (isPlaying) {
        controlsTimeoutRef.current = setTimeout(() => {
          setShowControls(false)
        }, 3000)
      }
    }

    resetControlsTimeout()

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [isPlaying])

  return (
    <>
      {/* Custom Styles */}
      <style jsx>{`
        .slider {
          -webkit-appearance: none;
          appearance: none;
          background: rgba(255, 255, 255, 0.3);
          outline: none;
          border-radius: 5px;
        }

        .slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid white;
        }

        .slider::-moz-range-thumb {
          width: 15px;
          height: 15px;
          border-radius: 50%;
          background: #3b82f6;
          cursor: pointer;
          border: 2px solid white;
        }

        .video-container:fullscreen {
          background: black;
        }

        .video-container:fullscreen video {
          width: 100vw;
          height: 100vh;
          object-fit: contain;
        }
      `}</style>

      <div className='video-player flex z-10 fixed m-auto left-0 right-0 top-0 bottom-0 min-w-[600px] max-w-[800px] h-fit'>
          {/* <div
            onClick={handleVideoPlayerClose}
            className=" flex z-10 items-center justify-center absolute top-0 right-[104px] h-[75px] w-[96px] cursor-pointer"
          >
            <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
          </div>  */}
        <div className='flex relative m-auto w-[calc(100%-75px)] h-[calc(100%-75px)] items-center justify-center overflow-y-auto overflow-x-hidden'>
          <div className="relative w-full max-w-4xl bg-black rounded-xl shadow-2xl overflow-hidden">
              {/* Video Container */}
              <div
                className="relative w-full aspect-video bg-black group cursor-pointer"
                onClick={togglePlayPause}
                onMouseMove={() => setShowControls(true)}
              >
                {/* Main Video Element */}
                <video
                  ref={videoRef}
                  src={data?.url}
                  poster={data?.thumbnail}
                  className="w-full h-full object-contain"
                  onLoadedMetadata={() => {
                    if (videoRef.current) {
                      setDuration(videoRef.current.duration)
                      setIsLoading(false)
                      videoRef.current.volume = volume
                    }
                  }}
                  onTimeUpdate={() => {
                    if (videoRef.current) {
                      setCurrentTime(videoRef.current.currentTime)
                    }
                  }}
                  onPlay={() => {
                    setIsPlaying(true)
                    setIsBuffering(false)
                  }}
                  onPause={() => setIsPlaying(false)}
                  onWaiting={() => setIsBuffering(true)}
                  onCanPlay={() => setIsBuffering(false)}
                  onError={() => {
                    setHasError(true)
                    setIsLoading(false)
                  }}
                  onEnded={() => setIsPlaying(false)}
                />

                {/* Loading Spinner */}
                {(isLoading || isBuffering) && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
                    <div className="animate-spin rounded-full h-16 w-16 border-4 border-white border-t-transparent"></div>
                  </div>
                )}

                {/* Error State */}
                {hasError && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-75">
                    <div className="text-center text-white">
                      <svg className="mx-auto h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h3 className="text-lg font-medium mb-2">Video Error</h3>
                      <p className="text-sm text-gray-300">Unable to load video. Please try again.</p>
                    </div>
                  </div>
                )}

                {/* Center Play Button (when paused) */}
                {!isPlaying && !isLoading && !hasError && (
                  <div onClick={togglePlayPause} className="absolute inset-0 flex items-center justify-center">
                    <div className='flex justify-center items-center w-24 h-24 border-2 border-white bg-gray-700/50 rounded-full'>
                      {isPlaying ? (
                          <svg className="h-20 w-20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                          </svg>
                        ) : (
                          <svg className="h-20 w-20" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z"/>
                          </svg>
                        )}
                    </div>
                  </div>
                )}

                {/* Video Controls Overlay */}
                <div className={`absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black via-black/50 to-transparent p-4 transition-opacity duration-300 ${showControls ? 'opacity-100' : 'opacity-0'}`}>

                  {/* Progress Bar */}
                  <div className="w-full">
                    <div
                      ref={progressRef}
                      className="w-full h-1 bg-white bg-opacity-30 rounded-full cursor-pointer hover:h-3 transition-all duration-200"
                      onClick={handleSeek}
                    >
                      <div
                        className="h-full bg-blue-500 rounded-full transition-all duration-200"
                        style={{ width: `${duration ? (currentTime / duration) * 100 : 0}%` }}
                      />
                    </div>
                  </div>
                  {/* Control Buttons */}
                  <div className="flex flex-col items-center justify-between">
                    <div className='flex w-full h-fit justify-between'>
                      {/* Left Controls */}
                      <div className='flex max-w-1/3 flex-col justify-center'>
                        {/* Video COntrols Bar */}
                        <div className="flex items-center space-x-4">
                          {/* Play/Pause */}
                          <button
                            onClick={togglePlayPause}
                            className="text-white hover:text-blue-400 transition-colors p-2"
                            aria-label={isPlaying ? 'Pause' : 'Play'}
                          >
                            {isPlaying ? (
                              <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
                              </svg>
                            ) : (
                              <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M8 5v14l11-7z"/>
                              </svg>
                            )}
                          </button>

                          {/* Skip Backward */}
                          <button
                            onClick={() => skipTime(-10)}
                            className="text-white hover:text-blue-400 transition-colors p-2"
                            aria-label="Skip backward 10 seconds"
                          >
                            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M11.99 5V1l-5 5 5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6h-2c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
                              <text x="12" y="15" textAnchor="middle" fontSize="8" fill="white">10</text>
                            </svg>
                          </button>

                          {/* Skip Forward */}
                          <button
                            onClick={() => skipTime(10)}
                            className="text-white hover:text--400 transition-colors p-2"
                            aria-label="Skip forward 10 seconds"
                          >
                            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M12 5V1l5 5-5 5V7c-3.31 0-6 2.69-6 6s2.69 6 6 6 6-2.69 6-6h2c0 4.42-3.58 8-8 8s-8-3.58-8-8 3.58-8 8-8z"/>
                              <text x="12" y="15" textAnchor="middle" fontSize="8" fill="white">10</text>
                            </svg>
                          </button>

                          {/* Volume Controls */}
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={toggleMute}
                              className="text-white hover:text-blue-400 transition-colors p-2"
                              aria-label={isMuted ? 'Unmute' : 'Mute'}
                            >
                              {isMuted || volume === 0 ? (
                                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M16.5 12c0-1.77-1.02-3.29-2.5-4.03v2.21l2.45 2.45c.03-.2.05-.41.05-.63zm2.5 0c0 .94-.2 1.82-.54 2.64l1.51 1.51C20.63 14.91 21 13.5 21 12c0-4.28-2.99-7.86-7-8.77v2.06c2.89.86 5 3.54 5 6.71zM4.27 3L3 4.27 7.73 9H3v6h4l5 5v-6.73l4.25 4.25c-.67.52-1.42.93-2.25 1.18v2.06c1.38-.31 2.63-.95 3.69-1.81L19.73 21 21 19.73l-9-9L4.27 3zM12 4L9.91 6.09 12 8.18V4z"/>
                                </svg>
                              ) : (
                                <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
                                </svg>
                              )}
                            </button>

                            <input
                              ref={volumeRef}
                              type="range"
                              min="0"
                              max="1"
                              step="0.1"
                              value={isMuted ? 0 : volume}
                              onChange={handleVolumeChange}
                              className="w-20 h-1 bg-white bg-opacity-30 rounded-lg appearance-none cursor-pointer slider"
                              aria-label="Volume"
                            />
                          </div>

                          {/* Time Display */}
                          <div className="text-white text-sm font-mono">
                            {formatTime(currentTime)} / {formatTime(duration)}
                          </div>
                        </div>
                      </div>

                      {/* Right Controls */}
                      {true && <div className="flex items-center space-x-2">
                        {/* Fullscreen Toggle */}
                        <button
                          onClick={toggleFullscreen}
                          className="text-white hover:text-blue-400 transition-colors p-2"
                          aria-label={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
                        >
                          {isFullscreen ? (
                            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M5 16h3v3h2v-5H5v2zm3-8H5v2h5V5H8v3zm6 11h2v-3h3v-2h-5v5zm2-11V5h-2v5h5V8h-3z"/>
                            </svg>
                          ) : (
                            <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z"/>
                            </svg>
                          )}
                        </button>
                      </div>}
                    </div>
                    {
                      <div className='flex w-full flex-col justify-center mt-4'>
                        <h1 className='font-bold uppercase leading-5 text-lg'>{data?.title}</h1>
                        <span className='text-sm'>{data?.description}</span>
                      </div>
                    }
                  </div>
                </div>
              </div>
          </div>
        </div>
      </div>
    </>
  )
}
