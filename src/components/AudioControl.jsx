'use client';

import React from 'react';
import { useGlobalAudioSafe } from '@/contexts/GlobalAudioContext';

/**
 * AudioControl Component
 * 
 * A simple control component that can be placed anywhere in your app
 * to control the global audio player. Perfect for adding to navigation bars,
 * 360 viewers, or any page that needs audio controls.
 */
const AudioControl = ({ 
  variant = 'button', // 'button', 'toggle', 'icon-only'
  size = 'medium', // 'small', 'medium', 'large'
  className = ""
}) => {
  const globalAudio = useGlobalAudioSafe();

  // If no global audio context, don't render
  if (!globalAudio) {
    return null;
  }

  const { isPlaying, isLoaded, togglePlayPause, error } = globalAudio;

  // Size classes
  const sizeClasses = {
    small: 'w-8 h-8 text-sm',
    medium: 'w-10 h-10 text-base',
    large: 'w-12 h-12 text-lg'
  };

  const iconSizeClasses = {
    small: 'w-4 h-4',
    medium: 'w-5 h-5',
    large: 'w-6 h-6'
  };

  const sizeClass = sizeClasses[size] || sizeClasses.medium;
  const iconSizeClass = iconSizeClasses[size] || iconSizeClasses.medium;

  if (variant === 'icon-only') {
    return (
      <button
        onClick={togglePlayPause}
        disabled={!isLoaded}
        className={`${sizeClass} flex items-center justify-center rounded-full bg-blue-500 text-white hover:bg-blue-600 disabled:bg-gray-400 transition-colors ${className}`}
        title={isPlaying ? 'Pause Audio' : 'Play Audio'}
      >
        {isPlaying ? (
          <svg className={iconSizeClass} fill="currentColor" viewBox="0 0 24 24">
            <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
          </svg>
        ) : (
          <svg className={iconSizeClass} fill="currentColor" viewBox="0 0 24 24">
            <path d="M8 5v14l11-7z"/>
          </svg>
        )}
      </button>
    );
  }

  if (variant === 'toggle') {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <span className="text-sm text-gray-600">Audio:</span>
        <button
          onClick={togglePlayPause}
          disabled={!isLoaded}
          className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
            isPlaying 
              ? 'bg-green-100 text-green-800 hover:bg-green-200' 
              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
          } disabled:bg-gray-100 disabled:text-gray-400`}
        >
          {!isLoaded ? 'Loading...' : isPlaying ? 'ON' : 'OFF'}
        </button>
        {error && (
          <span className="text-xs text-red-500" title={error}>⚠️</span>
        )}
      </div>
    );
  }

  // Default button variant
  return (
    <button
      onClick={togglePlayPause}
      disabled={!isLoaded}
      className={`flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 transition-colors ${className}`}
    >
      {isPlaying ? (
        <svg className={iconSizeClass} fill="currentColor" viewBox="0 0 24 24">
          <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
        </svg>
      ) : (
        <svg className={iconSizeClass} fill="currentColor" viewBox="0 0 24 24">
          <path d="M8 5v14l11-7z"/>
        </svg>
      )}
      <span className="text-sm font-medium">
        {!isLoaded ? 'Loading...' : isPlaying ? 'Pause' : 'Play'}
      </span>
    </button>
  );
};

/**
 * AudioStatusIndicator Component
 * 
 * A simple status indicator that shows the current audio state
 * without any controls. Perfect for showing audio status in headers.
 */
export const AudioStatusIndicator = ({ className = "" }) => {
  const globalAudio = useGlobalAudioSafe();

  if (!globalAudio) {
    return null;
  }

  const { isPlaying, isLoaded, currentTrack } = globalAudio;

  return (
    <div className={`flex items-center space-x-2 text-sm text-gray-600 ${className}`}>
      <div className={`w-2 h-2 rounded-full ${isPlaying ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`} />
      <span>
        {!isLoaded && 'Loading audio...'}
        {isLoaded && !isPlaying && 'Audio ready'}
        {isLoaded && isPlaying && `Playing: ${currentTrack?.title || 'Audio'}`}
      </span>
    </div>
  );
};

export default AudioControl;
