/**
 * Audio Cache Service Worker
 * 
 * Provides persistent caching for audio files to ensure
 * they're available even on subsequent visits.
 */

const CACHE_NAME = 'elephant-island-audio-v1';
const AUDIO_CACHE_NAME = 'elephant-island-audio-files-v1';

// Audio files to cache
const AUDIO_FILES = [
  '/assets/Ambient_Music.mp4',
  // Add more audio files here as needed
];

// Install event - cache audio files
self.addEventListener('install', (event) => {
  console.log('🔧 Audio Cache Service Worker installing...');
  
  event.waitUntil(
    (async () => {
      try {
        const cache = await caches.open(AUDIO_CACHE_NAME);
        
        console.log('📥 Caching audio files...');
        
        // Cache audio files with error handling
        const cachePromises = AUDIO_FILES.map(async (url) => {
          try {
            const response = await fetch(url, {
              mode: 'cors',
              credentials: 'omit'
            });
            
            if (response.ok) {
              await cache.put(url, response);
              console.log('✅ Cached audio file:', url);
            } else {
              console.warn('⚠️ Failed to fetch audio file:', url, response.status);
            }
          } catch (error) {
            console.error('❌ Error caching audio file:', url, error);
          }
        });
        
        await Promise.allSettled(cachePromises);
        console.log('🎵 Audio caching completed');
        
        // Skip waiting to activate immediately
        self.skipWaiting();
        
      } catch (error) {
        console.error('❌ Service worker install failed:', error);
      }
    })()
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('🚀 Audio Cache Service Worker activating...');
  
  event.waitUntil(
    (async () => {
      try {
        // Clean up old caches
        const cacheNames = await caches.keys();
        const deletePromises = cacheNames
          .filter(name => name.startsWith('elephant-island-audio-') && name !== AUDIO_CACHE_NAME)
          .map(name => {
            console.log('🗑️ Deleting old cache:', name);
            return caches.delete(name);
          });
        
        await Promise.all(deletePromises);
        
        // Take control of all clients
        await self.clients.claim();
        
        console.log('✅ Audio Cache Service Worker activated');
        
      } catch (error) {
        console.error('❌ Service worker activation failed:', error);
      }
    })()
  );
});

// Fetch event - serve cached audio files
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);
  
  // Only handle audio file requests
  if (isAudioRequest(event.request)) {
    event.respondWith(handleAudioRequest(event.request));
  }
});

/**
 * Check if request is for an audio file
 */
function isAudioRequest(request) {
  const url = new URL(request.url);
  const pathname = url.pathname;
  
  // Check if it's one of our cached audio files
  return AUDIO_FILES.some(audioFile => pathname.includes(audioFile)) ||
         pathname.includes('/assets/') && (
           pathname.endsWith('.mp3') ||
           pathname.endsWith('.mp4') ||
           pathname.endsWith('.wav') ||
           pathname.endsWith('.ogg') ||
           pathname.endsWith('.m4a')
         );
}

/**
 * Handle audio file requests with cache-first strategy
 */
async function handleAudioRequest(request) {
  try {
    // Try cache first
    const cache = await caches.open(AUDIO_CACHE_NAME);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      console.log('🎵 Serving audio from cache:', request.url);
      return cachedResponse;
    }
    
    // If not in cache, fetch from network
    console.log('📥 Fetching audio from network:', request.url);
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      const responseClone = networkResponse.clone();
      await cache.put(request, responseClone);
      console.log('✅ Cached new audio file:', request.url);
    }
    
    return networkResponse;
    
  } catch (error) {
    console.error('❌ Error handling audio request:', error);
    
    // Return a basic error response
    return new Response('Audio file not available', {
      status: 503,
      statusText: 'Service Unavailable'
    });
  }
}

// Message handling for cache management
self.addEventListener('message', (event) => {
  const { type, data } = event.data;
  
  switch (type) {
    case 'CACHE_AUDIO':
      handleCacheAudioMessage(data, event);
      break;
      
    case 'CLEAR_AUDIO_CACHE':
      handleClearCacheMessage(event);
      break;
      
    case 'GET_CACHE_STATUS':
      handleGetCacheStatusMessage(event);
      break;
      
    default:
      console.log('Unknown message type:', type);
  }
});

/**
 * Handle cache audio message
 */
async function handleCacheAudioMessage(audioUrls, event) {
  try {
    const cache = await caches.open(AUDIO_CACHE_NAME);
    
    const cachePromises = audioUrls.map(async (url) => {
      try {
        const response = await fetch(url);
        if (response.ok) {
          await cache.put(url, response);
          return { url, success: true };
        } else {
          return { url, success: false, error: `HTTP ${response.status}` };
        }
      } catch (error) {
        return { url, success: false, error: error.message };
      }
    });
    
    const results = await Promise.all(cachePromises);
    
    event.ports[0].postMessage({
      type: 'CACHE_AUDIO_COMPLETE',
      results
    });
    
  } catch (error) {
    event.ports[0].postMessage({
      type: 'CACHE_AUDIO_ERROR',
      error: error.message
    });
  }
}

/**
 * Handle clear cache message
 */
async function handleClearCacheMessage(event) {
  try {
    await caches.delete(AUDIO_CACHE_NAME);
    
    event.ports[0].postMessage({
      type: 'CLEAR_CACHE_COMPLETE'
    });
    
  } catch (error) {
    event.ports[0].postMessage({
      type: 'CLEAR_CACHE_ERROR',
      error: error.message
    });
  }
}

/**
 * Handle get cache status message
 */
async function handleGetCacheStatusMessage(event) {
  try {
    const cache = await caches.open(AUDIO_CACHE_NAME);
    const keys = await cache.keys();
    
    const status = {
      totalCached: keys.length,
      cachedUrls: keys.map(request => request.url)
    };
    
    event.ports[0].postMessage({
      type: 'CACHE_STATUS',
      status
    });
    
  } catch (error) {
    event.ports[0].postMessage({
      type: 'CACHE_STATUS_ERROR',
      error: error.message
    });
  }
}
